import { IKCallback } from "../interfaces/IKCallback";
import { ImageKitOptions, PurgeCacheResponse, PurgeCacheStatusResponse } from "../interfaces";
declare const _default: {
    purgeCache: (url: string, defaultOptions: ImageKitOptions, callback?: IKCallback<PurgeCacheResponse, Error> | undefined) => void;
    getPurgeCacheStatus: (requestId: string, defaultOptions: ImageKitOptions, callback?: IKCallback<PurgeCacheStatusResponse, Error> | undefined) => void;
};
export default _default;
