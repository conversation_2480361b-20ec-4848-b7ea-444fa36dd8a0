import React, { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import SEO, { pageSEO } from '../components/SEO';
import Logo from '../components/Logo';
import { <PERSON>rkles, Zap, Heart, Star, ArrowRight, Palette, Wand2, Image as ImageIcon, Camera, Users, Award, Shield } from 'lucide-react';

// Welcome Screen Component
const WelcomeScreen = () => {
  const navigate = useNavigate();

  const handleGetStarted = () => {
    navigate('/generate');
  };

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <div className="max-w-6xl mx-auto px-4 pt-20 pb-16">
        <div className="text-center">
          {/* Main Title */}
          <h1 className="text-4xl md:text-6xl lg:text-7xl font-bold mb-6 animate-fade-in-up">
            <span className="text-white">Create images using </span>
            <span className="bg-gradient-to-r from-blue-400 via-purple-500 to-pink-500 bg-clip-text text-transparent">AI</span>
          </h1>

          {/* Subtitle */}
          <h2 className="text-2xl md:text-3xl lg:text-4xl font-semibold text-white mb-8 animate-fade-in-up" style={{ animationDelay: '0.2s' }}>
            Generate professional portraits and themed headshots
          </h2>

          {/* Description */}
          <p className="text-lg md:text-xl text-gray-300 mb-12 max-w-4xl mx-auto leading-relaxed animate-fade-in-up" style={{ animationDelay: '0.4s' }}>
           Experience the best AI image generator from text. Our free AI image generator lets you describe anything and turn it into visuals instantly. No sign-up needed. Try image generator AI free today.
          </p>

          {/* CTA Button */}
          <div className="mb-10 animate-fade-in-up" style={{ animationDelay: '0.6s' }}>
            <button
              onClick={handleGetStarted}
              className="group relative inline-flex items-center gap-3 px-12 py-4 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white text-xl font-bold rounded-full shadow-2xl hover:shadow-3xl transform hover:scale-105 transition-all duration-300"
            >
              <Camera className="w-6 h-6 group-hover:rotate-12 transition-transform duration-300" />
              <span>Generate Now</span>
              <ArrowRight className="w-6 h-6 group-hover:translate-x-1 transition-transform duration-300" />
            </button>
          </div>

          {/* Laptop Image */}
          <div className="mb-10 animate-fade-in-up" style={{ animationDelay: '0.8s' }}>
            <div className="max-w-4xl mx-auto">
              <img
                src="/laptop.png"
                alt="AI Image Generator Interface"
                className="w-full h-auto rounded-2xl shadow-2xl hover:shadow-3xl transition-all duration-500 hover:scale-105"
              />
            </div>
          </div>

        </div>
      </div>

      {/* Gaming Avatar Section */}
      <div className="bg-gradient-to-br from-purple-900/20 via-blue-900/20 to-indigo-900/20 py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6 animate-fade-in-up">
              <span className="bg-gradient-to-r from-purple-400 via-pink-500 to-red-500 bg-clip-text text-transparent">
                Become the Star of Your Own Universe
              </span>
            </h2>
          </div>

          <div className="grid lg:grid-cols-2 gap-12 items-center">
            {/* Left Side - Image */}
            <div className="order-1 lg:order-1 animate-fade-in-up" style={{ animationDelay: '0.2s' }}>
              <div className="relative group">
                <div className="absolute -inset-4 bg-gradient-to-r from-purple-600 via-pink-600 to-red-600 rounded-3xl blur-xl opacity-30 group-hover:opacity-50 transition-opacity duration-500"></div>
                <div className="relative bg-gradient-to-br from-purple-900/50 to-pink-900/50 p-4 rounded-3xl backdrop-blur-sm border border-purple-500/30">
                  <img
                    src="/gameAvter.png"
                    alt="Gaming Avatar Creation"
                    className="w-full h-auto rounded-2xl shadow-2xl hover:scale-105 transition-all duration-500"
                  />
                </div>
              </div>
            </div>

            {/* Right Side - Content */}
            <div className="order-2 lg:order-2 animate-fade-in-up" style={{ animationDelay: '0.4s' }}>
              <div className="space-y-8">
                <div>
                  <h3 className="text-3xl md:text-4xl font-bold text-white mb-4">
                    Gaming Avatars
                  </h3>
                  <p className="text-xl md:text-2xl text-purple-300 font-semibold mb-6">
                    Bring Your In-Game Identity to Life
                  </p>
                  <p className="text-lg text-gray-300 leading-relaxed mb-8">
                    Create unique, high-quality gaming avatars using our Free Text-to-Image Generator — no design skills needed!
                  </p>
                </div>

                <div className="space-y-6">
                  <div className="flex items-start gap-4 group">
                    <div className="flex-shrink-0 w-8 h-8 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                      <span className="text-white font-bold">🔹</span>
                    </div>
                    <p className="text-gray-300 text-lg leading-relaxed">
                      <span className="text-white font-semibold">Customize your look</span> with detailed prompts and unique styles
                    </p>
                  </div>

                  <div className="flex items-start gap-4 group">
                    <div className="flex-shrink-0 w-8 h-8 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                      <span className="text-white font-bold">🔹</span>
                    </div>
                    <p className="text-gray-300 text-lg leading-relaxed">
                      <span className="text-white font-semibold">Perfect for streamers, gamers,</span> and online profiles
                    </p>
                  </div>

                  <div className="flex items-start gap-4 group">
                    <div className="flex-shrink-0 w-8 h-8 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                      <span className="text-white font-bold">🔹</span>
                    </div>
                    <p className="text-gray-300 text-lg leading-relaxed">
                      <span className="text-white font-semibold">Stand out in any game world</span> with AI-powered avatar creation
                    </p>
                  </div>
                </div>

                <div className="pt-8">
                  <button
                    onClick={handleGetStarted}
                    className="group inline-flex items-center gap-3 px-8 py-4 bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white text-lg font-bold rounded-full shadow-2xl hover:shadow-3xl transform hover:scale-105 transition-all duration-300"
                  >
                    <Wand2 className="w-6 h-6 group-hover:rotate-12 transition-transform duration-300" />
                    <span>Create Now</span>
                    <ArrowRight className="w-6 h-6 group-hover:translate-x-1 transition-transform duration-300" />
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Features Section */}
      <div className="max-w-6xl mx-auto px-4 py-16">
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
          <div className="text-center group">
            <div className="w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
              <Camera className="w-8 h-8 text-white" />
            </div>
            <h3 className="text-xl font-semibold text-white mb-2">Professional Portraits</h3>
            <p className="text-gray-400">Perfect for LinkedIn, resumes, and business profiles</p>
          </div>

          <div className="text-center group">
            <div className="w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-purple-500 to-pink-600 rounded-2xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
              <Users className="w-8 h-8 text-white" />
            </div>
            <h3 className="text-xl font-semibold text-white mb-2">Social Media Ready</h3>
            <p className="text-gray-400">Instagram, Tinder, and dating app profile pictures</p>
          </div>

          <div className="text-center group">
            <div className="w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-pink-500 to-red-600 rounded-2xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
              <Award className="w-8 h-8 text-white" />
            </div>
            <h3 className="text-xl font-semibold text-white mb-2">High Quality</h3>
            <p className="text-gray-400">Professional-grade AI generated images</p>
          </div>

          <div className="text-center group">
            <div className="w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-green-500 to-blue-600 rounded-2xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
              <Shield className="w-8 h-8 text-white" />
            </div>
            <h3 className="text-xl font-semibold text-white mb-2">100% Free</h3>
            <p className="text-gray-400">No signup required, unlimited generations</p>
          </div>
        </div>
      </div>

      {/* How It Works Section */}
      <div className="max-w-6xl mx-auto px-4 py-16">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">How It Works</h2>
          <p className="text-xl text-gray-300">Create professional headshots in 3 simple steps</p>
        </div>

        <div className="grid md:grid-cols-3 gap-8">
          <div className="text-center">
            <div className="w-12 h-12 mx-auto mb-4 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white font-bold text-xl">
              1
            </div>
            <h3 className="text-xl font-semibold text-white mb-2">Describe Your Vision</h3>
            <p className="text-gray-400">Tell our AI what kind of headshot you want - professional, casual, creative, or themed</p>
          </div>

          <div className="text-center">
            <div className="w-12 h-12 mx-auto mb-4 bg-gradient-to-r from-purple-500 to-pink-600 rounded-full flex items-center justify-center text-white font-bold text-xl">
              2
            </div>
            <h3 className="text-xl font-semibold text-white mb-2">AI Creates Magic</h3>
            <p className="text-gray-400">Our advanced AI processes your request and generates stunning, realistic portraits</p>
          </div>

          <div className="text-center">
            <div className="w-12 h-12 mx-auto mb-4 bg-gradient-to-r from-pink-500 to-red-600 rounded-full flex items-center justify-center text-white font-bold text-xl">
              3
            </div>
            <h3 className="text-xl font-semibold text-white mb-2">Download & Use</h3>
            <p className="text-gray-400">Get your high-resolution headshots instantly and use them anywhere you need</p>
          </div>
        </div>
      </div>

    </div>
  );
};

const Home = () => {
  useEffect(() => {
    // Scroll to top when page loads
    window.scrollTo({ top: 0, behavior: 'smooth' });
  }, []);

  return (
    <div className="relative min-h-screen">
      {/* SEO */}
      <SEO
        title={pageSEO.home.title}
        description={pageSEO.home.description}
        keywords={pageSEO.home.keywords}
      />

      {/* Background gradient - Dark theme like PhotoGPT */}
      <div className="absolute inset-0 bg-gradient-to-br from-gray-900 via-gray-800 to-black -z-10"></div>

      {/* Subtle pattern overlay */}
      <div className="absolute inset-0 opacity-10 -z-5" style={{
        backgroundImage: `radial-gradient(circle at 25% 25%, rgba(59, 130, 246, 0.3) 0%, transparent 50%),
                         radial-gradient(circle at 75% 75%, rgba(147, 51, 234, 0.3) 0%, transparent 50%)`
      }}></div>

      {/* Content container */}
      <div className="relative z-10">
        <WelcomeScreen />
      </div>
    </div>
  );
};

export default Home;
