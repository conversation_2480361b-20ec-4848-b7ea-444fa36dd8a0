const Replicate = require("replicate");

const aiservice = async(prompt, aspectRatio = '1:1')=>{
try {
    const replicate = new Replicate({
  auth: process.env.AI_KEY,
});

const output =  await replicate.run(
  "prunaai/flux.1-dev:786b08f5ce6469390ec0cd9164bde74d02a2af548316821eba0bbeb347b421df",
  {
    input: {
    aspect_ratio: aspectRatio,
    prompt: prompt,
    seed: -1,
    output_format: "jpg",
    output_quality: 80,
    num_inference_steps: 85, 
    guidance: 7,
    image_size: 1024,
    speed_mode: "Juiced 🔥 (default)",

      
    }
  }
);

// To access the file URL:
// const imageUrlObject = urls[0];
const imageUrl = output.url() 

// Convert URL object to string

return imageUrl;

} catch (error) {
    console.error('❌ AI service error:', error);
}

}
module.exports = aiservice;