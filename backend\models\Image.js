const mongoose = require('mongoose');

const imageSchema = new mongoose.Schema({
  prompt: {
    type: String,
    required: true,
    trim: true
  },
  imageUrl: {
    type: String,
    required: true
  },
  imageKitUrl: {
    type: String,
    default: ''
  },
  imageKitFileId: {
    type: String,
    default: ''
  },
  imageKitFilePath: {
    type: String,
    default: ''
  },
  aspectRatio: {
    type: String,
    required: true,
    enum: ['1:1', '3:4', '4:3', '9:16', '16:9', '2:3', '3:2']
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  userAgent: {
    type: String,
    default: ''
  },
  ipAddress: {
    type: String,
    default: ''
  }
}, {
  timestamps: true
});

// Index for better query performance
imageSchema.index({ createdAt: -1 });
imageSchema.index({ aspectRatio: 1 });
imageSchema.index({ prompt: 'text' });

module.exports = mongoose.model('Image', imageSchema);
