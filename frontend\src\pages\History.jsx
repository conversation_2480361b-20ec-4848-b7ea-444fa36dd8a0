import React, { useState, useEffect } from 'react';
import { Search, Download, Trash2, Calendar, Image as ImageIcon, AlertCircle, Home, Clock, Filter, Grid, Heart, Share2, Eye, Star, <PERSON>rk<PERSON>, <PERSON>lette, Zap } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { getImagesFromLocalStorage } from '../utils/api';
import {
  formatDate,
  isImageExpiring,
  isImageExpired,
  getTimeUntilExpiration
} from '../utils/history';
import SEO, { pageSEO } from '../components/SEO';
// import FullscreenImageViewer from '../components/FullscreenImageViewer'; // Removed fullscreen functionality
import ShareButton from '../components/ShareButton';
import Breadcrumbs, { BreadcrumbStructuredData } from '../components/Breadcrumbs';


const History = () => {
  const [history, setHistory] = useState([]);
  const [filteredHistory, setFilteredHistory] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [isLoading, setIsLoading] = useState(true);
  const [stats, setStats] = useState({ totalImages: 0 });
  // Removed fullscreen state variables
  // Removed download modal states

  const navigate = useNavigate();

  useEffect(() => {
    loadHistory();
    // Scroll to top when page loads
    window.scrollTo({ top: 0, behavior: 'smooth' });
  }, []);



  // No expiration tracking needed for ImageKit images

  const loadHistory = () => {
    setIsLoading(true);
    try {
      const historyData = getImagesFromLocalStorage();
      console.log('📖 Loading history from localStorage:', historyData.length, 'images');
      const statsData = {
        totalImages: historyData.length,
        oldestImage: historyData.length > 0 ? historyData[historyData.length - 1].createdAt : null,
        newestImage: historyData.length > 0 ? historyData[0].createdAt : null,
      };
      const expiringImagesCount = 0; // ImageKit URLs don't expire
      setHistory(historyData);
      setFilteredHistory(historyData);
      setStats(statsData);
      setExpiringCount(expiringImagesCount);
    } catch (error) {
      // Error loading history
    } finally {
      setIsLoading(false);
    }
  };

  const handleDelete = (imageId) => {
    if (window.confirm('Are you sure you want to delete this image from history?')) {
      try {
        const images = JSON.parse(localStorage.getItem('generatedImages') || '[]');
        const updatedImages = images.filter(img => (img.timestamp || Date.now()) !== imageId);
        localStorage.setItem('generatedImages', JSON.stringify(updatedImages));
        loadHistory();
      } catch (error) {
        console.error('❌ Failed to delete image:', error);
      }
    }
  };

  const handleClearAll = () => {
    if (window.confirm('Are you sure you want to clear all history? This action cannot be undone.')) {
      try {
        localStorage.removeItem('generatedImages');
        loadHistory();
        setSearchTerm('');
      } catch (error) {
        console.error('❌ Failed to clear history:', error);
      }
    }
  };

  const handleDownloadClick = (item) => {
    // Navigate to download page using React Router
    const downloadUrl = `/image/download?url=${encodeURIComponent(item.imageUrl)}&prompt=${encodeURIComponent(item.prompt)}`;
    navigate(downloadUrl);
  };



  // Removed fullscreen functionality

  const handleShare = () => {
    // You can add analytics or other tracking here
  };

  useEffect(() => {
    if (!searchTerm.trim()) {
      setFilteredHistory(history);
    } else {
      const filtered = history.filter(item =>
        item.prompt.toLowerCase().includes(searchTerm.toLowerCase())
      );
      setFilteredHistory(filtered);
    }
  }, [searchTerm, history]);

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-600 mx-auto"></div>
          <p className="mt-4 text-gray-300">Loading your history...</p>
        </div>
      </div>
    );
  }

  // Create breadcrumbs for history page
  const historyBreadcrumbs = [
    { label: 'Home', href: '/', icon: Home },
    { label: 'Generation History', href: null, icon: Clock }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 py-8 animate-fade-in relative overflow-hidden">
      {/* SEO */}
      <SEO
        title={pageSEO.history.title}
        description={pageSEO.history.description}
        keywords={pageSEO.history.keywords}
      />

      {/* Structured Data for Breadcrumbs */}
      <BreadcrumbStructuredData breadcrumbs={historyBreadcrumbs} />

      {/* Background Elements */}
      <div className="fixed inset-0 pointer-events-none overflow-hidden -z-10">
        <div className="absolute top-20 left-10 w-32 h-32 bg-purple-800 rounded-full opacity-10 animate-float" style={{ animationDelay: '0s' }}></div>
        <div className="absolute top-40 right-20 w-24 h-24 bg-pink-800 rounded-full opacity-10 animate-float" style={{ animationDelay: '2s' }}></div>
        <div className="absolute bottom-40 left-20 w-20 h-20 bg-orange-800 rounded-full opacity-10 animate-float" style={{ animationDelay: '4s' }}></div>
        <div className="absolute bottom-20 right-10 w-28 h-28 bg-blue-800 rounded-full opacity-10 animate-float" style={{ animationDelay: '6s' }}></div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        {/* Breadcrumbs */}
        <Breadcrumbs customBreadcrumbs={historyBreadcrumbs} />

        {/* Enhanced Header */}
        <div className="text-center mb-12">
          <div className="relative inline-block">
            <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold bg-gradient-to-r from-purple-600 via-pink-600 to-orange-500 bg-clip-text text-transparent animate-gradient-x mb-4">
              Your AI Gallery
            </h1>
            <div className="absolute -top-2 -right-2 animate-bounce">
              <Sparkles className="w-8 h-8 text-yellow-500" />
            </div>
          </div>

          <p className="text-xl text-gray-300 mb-6 max-w-2xl mx-auto">
            Explore your creative journey with {stats.totalImages} AI-generated masterpieces
          </p>

          {/* Stats Cards */}
          <div className="flex flex-wrap justify-center gap-4 mb-8">
            <div className="flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-purple-900 to-pink-900 rounded-full shadow-lg">
              <Palette className="w-5 h-5 text-purple-400" />
              <span className="font-semibold text-purple-300">{stats.totalImages} Creations</span>
            </div>

            <div className="flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-blue-900 to-cyan-900 rounded-full shadow-lg">
              <Zap className="w-5 h-5 text-blue-400" />
              <span className="font-semibold text-blue-300">AI Powered</span>
            </div>
          </div>
        </div>

        {/* ImageKit Storage Information */}
        <div className="bg-gradient-to-r from-green-900/20 via-emerald-800/10 to-green-900/20 border-2 border-green-800 rounded-2xl p-6 mb-8 shadow-lg">
          <div className="flex items-start gap-4">
            <div className="p-3 bg-green-900/50 rounded-xl flex-shrink-0">
              <Star className="w-6 h-6 text-green-400" />
            </div>
            <div className="flex-1">
              <h3 className="text-lg font-bold text-green-200 mb-3 flex items-center gap-2">
                ✅ Secure Cloud Storage
                <span className="px-2 py-1 bg-green-800 text-green-200 text-xs font-medium rounded-full">ImageKit CDN</span>
              </h3>
              <p className="text-base text-green-300 mb-4 leading-relaxed">
                <strong>Great news!</strong> Your images are now stored securely in our cloud storage with ImageKit CDN for optimal performance and reliability.
              </p>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3 text-sm text-green-400">
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  <span>Images stored permanently in secure cloud</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  <span>Fast loading with global CDN delivery</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  <span>Automatic image optimization</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  <span>No expiration - your images are safe</span>
                </div>
              </div>
              <div className="mt-4 p-3 bg-green-900/30 rounded-lg">
                <p className="text-sm text-green-300 font-medium">
                  🚀 Enhanced with professional CDN technology for the best user experience
                </p>
              </div>
            </div>
          </div>
        </div>



        {/* Enhanced Search and Actions */}
        <div className="bg-gradient-to-r from-gray-800 via-gray-700 to-gray-800 rounded-2xl shadow-xl border border-gray-600 p-6 sm:p-8 mb-8">
          <div className="flex flex-col lg:flex-row gap-6 items-center justify-between">
            {/* Enhanced Search */}
            <div className="relative flex-1 max-w-2xl">
              <div className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400">
                <Search className="w-6 h-6" />
              </div>
              <input
                type="text"
                placeholder="Search your AI creations..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-12 pr-6 py-4 border-2 border-gray-600 rounded-xl
                         bg-gray-700 text-white text-lg
                         focus:ring-2 focus:ring-purple-500 focus:border-purple-500 focus:bg-gray-600
                         transition-all duration-200 shadow-inner"
              />
            </div>

            {/* Action Buttons */}
            <div className="flex items-center gap-3">
              {/* View Toggle */}
              <div className="flex items-center bg-gray-700 rounded-xl p-1">
                <button className="p-2 rounded-lg bg-purple-600 text-white shadow-sm">
                  <Grid className="w-5 h-5" />
                </button>
              </div>

              {/* Clear All Button */}
              {history.length > 0 && (
                <button
                  onClick={handleClearAll}
                  className="flex items-center gap-2 px-6 py-3 bg-gradient-to-r from-red-600 to-red-700 hover:from-red-700 hover:to-red-800
                           text-white font-medium rounded-xl transition-all duration-200 shadow-lg hover:shadow-xl transform hover:scale-105"
                >
                  <Trash2 className="w-5 h-5" />
                  Clear All
                </button>
              )}
            </div>
          </div>
        </div>

        {/* Enhanced Empty State */}
        {filteredHistory.length === 0 ? (
          <div className="text-center py-20">
            <div className="relative">
              <div className="w-32 h-32 sm:w-40 sm:h-40 mx-auto bg-gradient-to-br from-purple-900 via-pink-900 to-orange-900 rounded-3xl flex items-center justify-center mb-8 shadow-lg">
                {searchTerm ? (
                  <Search className="w-16 h-16 sm:w-20 sm:h-20 text-purple-400" />
                ) : (
                  <ImageIcon className="w-16 h-16 sm:w-20 sm:h-20 text-purple-400" />
                )}
              </div>
              <div className="absolute -top-2 -right-2 animate-bounce">
                <Sparkles className="w-8 h-8 text-yellow-500" />
              </div>
            </div>

            <h3 className="text-2xl sm:text-3xl font-bold text-white mb-4">
              {searchTerm ? 'No creations found' : 'Your gallery awaits'}
            </h3>
            <p className="text-lg text-gray-300 mb-8 max-w-md mx-auto">
              {searchTerm
                ? 'Try different search terms or clear your search to see all images'
                : 'Start creating amazing AI art to fill your personal gallery'
              }
            </p>

            {!searchTerm && (
              <button
                onClick={() => navigate('/')}
                className="inline-flex items-center gap-2 px-6 py-3 bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white font-medium rounded-xl transition-all duration-200 shadow-lg hover:shadow-xl transform hover:scale-105"
              >
                <Palette className="w-5 h-5" />
                Create Your First Image
              </button>
            )}
          </div>
        ) : (
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
            {filteredHistory.map((item, index) => (
              <div key={item.id} className="bg-gradient-to-br from-gray-800 via-gray-700 to-gray-800 rounded-2xl shadow-lg hover:shadow-2xl overflow-hidden group
                                               animate-fade-in-up hover:scale-105 transition-all duration-500 border border-gray-600"
                       style={{ animationDelay: `${index * 50}ms` }}>
                {/* Enhanced Image Container */}
                <div className="relative aspect-square overflow-hidden">
                  <img
                    src={item.imageUrl}
                    alt={item.prompt}
                    className="w-full h-full object-cover transition-all duration-500 group-hover:scale-110"
                    onError={(e) => {
                      e.target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAwIiBoZWlnaHQ9IjQwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZjNmNGY2Ii8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCwgc2Fucy1zZXJpZiIgZm9udC1zaXplPSIxOCIgZmlsbD0iIzk5YTNhZiIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPkltYWdlIG5vdCBhdmFpbGFibGU8L3RleHQ+PC9zdmc+';
                    }}
                  />

                  {/* Gradient Overlay */}
                  <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

                  {/* Enhanced Status Badges */}
                  <div className="absolute top-3 left-3 flex flex-col gap-2">
                    {isImageExpiring(item.createdAt) && !isImageExpired(item.createdAt) && (
                      <div className="bg-gradient-to-r from-amber-500 to-orange-500 text-white text-xs px-3 py-1.5 rounded-full
                                     flex items-center gap-1 shadow-lg backdrop-blur-sm animate-pulse">
                        <AlertCircle className="w-3 h-3" />
                        <span className="font-medium">{getTimeUntilExpiration(item.createdAt)}</span>
                      </div>
                    )}

                    {isImageExpired(item.createdAt) && (
                      <div className="bg-gradient-to-r from-red-500 to-red-600 text-white text-xs px-3 py-1.5 rounded-full
                                     flex items-center gap-1 shadow-lg backdrop-blur-sm">
                        <AlertCircle className="w-3 h-3" />
                        <span className="font-medium">Expired</span>
                      </div>
                    )}
                  </div>

                  {/* Enhanced Action Buttons - Always visible on mobile, hover on desktop */}
                  <div className="absolute top-3 right-3 flex gap-2 opacity-100 md:opacity-0 md:group-hover:opacity-100 transition-all duration-300">
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        window.open(item.imageUrl, '_blank');
                      }}
                      className="p-2 md:p-2.5 bg-gray-800/95 backdrop-blur-sm hover:bg-gray-700 rounded-lg md:rounded-xl transition-all duration-200 shadow-lg hover:scale-110"
                      title="View Full Size"
                    >
                      <Eye className="w-3.5 h-3.5 md:w-4 md:h-4 text-gray-300" />
                    </button>

                    <ShareButton
                      imageUrl={item.imageUrl}
                      prompt={item.prompt}
                      onShare={handleShare}
                      className="p-2 md:p-2.5 bg-blue-600/95 backdrop-blur-sm hover:bg-blue-700 rounded-lg md:rounded-xl transition-all duration-200 shadow-lg hover:scale-110"
                    />

                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        handleDownloadClick(item);
                      }}
                      className="p-2 md:p-2.5 bg-green-600/95 backdrop-blur-sm hover:bg-green-700 rounded-lg md:rounded-xl transition-all duration-200 shadow-lg hover:scale-110"
                      title="Download"
                    >
                      <Download className="w-3.5 h-3.5 md:w-4 md:h-4 text-white" />
                    </button>

                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        handleDelete(item.id);
                      }}
                      className="p-2 md:p-2.5 bg-red-600/95 backdrop-blur-sm hover:bg-red-700 rounded-lg md:rounded-xl transition-all duration-200 shadow-lg hover:scale-110"
                      title="Delete"
                    >
                      <Trash2 className="w-3.5 h-3.5 md:w-4 md:h-4 text-white" />
                    </button>
                  </div>
                </div>

                {/* Enhanced Content */}
                <div className="p-5 space-y-3">
                  {/* Prompt */}
                  <p className="text-sm font-medium text-gray-200 line-clamp-2 leading-relaxed">
                    {item.prompt}
                  </p>

                  {/* Metadata */}
                  <div className="flex items-center justify-between text-xs text-gray-400">
                    <div className="flex items-center gap-1.5">
                      <Calendar className="w-3.5 h-3.5" />
                      <span className="font-medium">{formatDate(item.createdAt)}</span>
                    </div>
                    <div className="flex items-center gap-1.5">
                      <ImageIcon className="w-3.5 h-3.5" />
                      <span className="font-medium">{item.aspectRatio || item.imageSize || '1:1'}</span>
                    </div>
                  </div>

                  {/* Tags */}
                  <div className="flex items-center justify-between pt-2 border-t border-gray-700">
                    <div className="flex items-center gap-2">
                      <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gradient-to-r from-purple-900 to-pink-900 text-purple-300">
                        AI Generated
                      </span>
                    </div>
                    <div className="flex items-center gap-1 text-xs text-gray-500">
                      <Heart className="w-3 h-3" />
                      <span>0</span>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}


      </div>
    </div>
  );
};

export default History;
