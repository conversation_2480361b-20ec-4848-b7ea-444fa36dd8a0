import React, { useState, useEffect, useRef } from 'react';
import { Download, Wand2, Image as ImageIcon, AlertCircle, <PERSON>rkles, Zap, Palette, Star, ArrowRight, Copy, Heart, Eye, CheckCircle, Upload } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { generateImage, getImageKitUrl } from '../utils/api';
import { aspectRatios } from '../utils/prompts';
import { saveImageToHistory } from '../utils/history';
import { useNotification } from '../contexts/NotificationContext';
import LoadingSpinner, { ButtonSpinner } from './LoadingSpinner';
// import FullscreenImageViewer from './FullscreenImageViewer'; // Removed fullscreen functionality
import ShareButton from './ShareButton';


const ImageGenerator = () => {
  const [prompt, setPrompt] = useState('');
  const [aspectRatio, setAspectRatio] = useState('1:1');
  const [generatedImage, setGeneratedImage] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [lastPrompt, setLastPrompt] = useState('');
  const [placeholderIndex, setPlaceholderIndex] = useState(0);
  const [imageKitUrl, setImageKitUrl] = useState(null);
  const [isUploadingToImageKit, setIsUploadingToImageKit] = useState(false);
  const [imageKitUploadComplete, setImageKitUploadComplete] = useState(false);
  // Removed fullscreen state and download modal state

  const { showHistoryNotification } = useNotification();
  const navigate = useNavigate();
  const textareaRef = useRef(null);

  // Enhanced placeholder examples
  const placeholderExamples = [
    "A majestic dragon soaring through clouds at sunset, digital art style",
    "Modern minimalist logo design with geometric shapes and blue gradient",
    "Cozy coffee shop interior with warm lighting and vintage furniture",
    "Futuristic cityscape with neon lights and flying cars, cyberpunk style",
    "Beautiful landscape with mountains, lake reflection, and autumn colors",
    "Portrait of a wise old wizard with a long beard and magical staff",
    "Abstract art with flowing colors and dynamic brush strokes",
    "Cute cartoon character design for children's book illustration",
    "Professional headshot of a business person in modern office setting",
    "Fantasy castle on a floating island surrounded by magical clouds",
    "Steampunk mechanical robot with brass gears and steam effects",
    "Serene zen garden with cherry blossoms and peaceful pond",
    "Epic space battle with starships and colorful nebula background",
    "Vintage car driving through a neon-lit city street at night",
    "Magical forest with glowing mushrooms and fairy lights"
  ];

  // Rotate placeholder text every 3 seconds
  React.useEffect(() => {
    const interval = setInterval(() => {
      setPlaceholderIndex((prev) => (prev + 1) % placeholderExamples.length);
    }, 3000);

    return () => clearInterval(interval);
  }, [placeholderExamples.length]);

  // Auto-resize textarea function
  const autoResizeTextarea = () => {
    const textarea = textareaRef.current;
    if (textarea) {
      // Reset height to auto to get the correct scrollHeight
      textarea.style.height = 'auto';
      // Set height to scrollHeight with min and max constraints
      const newHeight = Math.max(120, Math.min(textarea.scrollHeight, 300)); // Min 120px, Max 300px
      textarea.style.height = `${newHeight}px`;
    }
  };

  // Auto-resize textarea when prompt changes
  useEffect(() => {
    autoResizeTextarea();
  }, [prompt]);



  const handleGenerate = async () => {
    const trimmedPrompt = prompt.trim();

    if (!trimmedPrompt) {
      setError('Please enter a prompt to generate an image.');
      return;
    }

    setIsLoading(true);
    setError(null);
    setGeneratedImage(null);
    setImageKitUrl(null);
    setImageKitUploadComplete(false);

    try {
      const result = await generateImage(trimmedPrompt, aspectRatio);

      if (result.success) {
        setGeneratedImage(result.imageUrl);
        setLastPrompt(trimmedPrompt);
        setIsUploadingToImageKit(true);

        // Note: We don't save to history here anymore
        // Only ImageKit URLs will be saved to localStorage after upload completes

        // Clear the prompt input after successful generation
        setPrompt('');

        // Check for ImageKit URL after a delay
        setTimeout(async () => {
          try {
            const imagekitResponse = await getImageKitUrl(result.imageUrl);
            if (imagekitResponse.success && imagekitResponse.imageKitUrl) {
              setImageKitUrl(imagekitResponse.imageKitUrl);
              setImageKitUploadComplete(true);

              // Show notification that ImageKit URL was saved to history
              showHistoryNotification(1);

              console.log('✅ ImageKit URL received and saved to history:', imagekitResponse.imageKitUrl);
            }
          } catch (error) {
            console.log('⚠️ Could not get ImageKit URL:', error.message);
          } finally {
            setIsUploadingToImageKit(false);
          }
        }, 3000); // Wait 3 seconds for ImageKit upload

      } else {
        setError(result.error);
      }
    } catch (err) {
      setError('An unexpected error occurred. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleDownloadClick = () => {
    // Navigate to download page using React Router
    if (generatedImage && lastPrompt) {
      const downloadUrl = `/image/download?url=${encodeURIComponent(generatedImage)}&prompt=${encodeURIComponent(lastPrompt)}`;
      navigate(downloadUrl);
    }
  };



  const handleKeyDown = (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleGenerate();
    }
  };

  // Removed fullscreen handlers

  const handleShare = () => {
    // You can add analytics or other tracking here
  };

  return (
    <div className="w-full max-w-7xl mx-auto p-4 sm:p-6">
      {/* Enhanced Header */}
      <div className="text-center space-y-6 mb-8 sm:mb-12">
        <div className="relative">
          <h1 className="text-3xl sm:text-4xl lg:text-5xl xl:text-6xl font-bold bg-gradient-to-r from-purple-600 via-pink-600 to-orange-500 bg-clip-text text-transparent animate-gradient-x px-2 leading-tight">
            Create Stunning AI Art
          </h1>
          <div className="absolute -top-2 -right-2 animate-bounce">
            <Sparkles className="w-6 h-6 sm:w-8 sm:h-8 text-yellow-500" />
          </div>
        </div>

        <p className="text-lg sm:text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed">
          Transform your ideas into beautiful images with our powerful AI generator.
          <span className="font-semibold text-purple-400"> Free, fast, and unlimited!</span>
        </p>

        {/* Feature Pills */}
        <div className="flex flex-wrap justify-center gap-3 sm:gap-4">
          <div className="flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-purple-900 to-pink-900 rounded-full">
            <Zap className="w-4 h-4 text-purple-400" />
            <span className="text-sm font-medium text-purple-300">Lightning Fast</span>
          </div>
          <div className="flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-blue-900 to-cyan-900 rounded-full">
            <Star className="w-4 h-4 text-blue-400" />
            <span className="text-sm font-medium text-blue-300">High Quality</span>
          </div>
          <div className="flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-green-900 to-emerald-900 rounded-full">
            <Heart className="w-4 h-4 text-green-400" />
            <span className="text-sm font-medium text-green-300">100% Free</span>
          </div>
        </div>
      </div>

      {/* Main Layout - Responsive Grid */}
      <div className="grid grid-cols-1 xl:grid-cols-2 gap-4 sm:gap-6 lg:gap-8 min-h-[500px] sm:min-h-[600px] w-full max-w-[1600px] mx-auto">

        {/* Left Side - Enhanced Controls */}
        <div className="w-full max-w-[800px] mx-auto bg-gradient-to-br from-gray-800 via-gray-700 to-gray-800 rounded-2xl p-4 sm:p-6 lg:p-8 space-y-4 sm:space-y-6 lg:space-y-8 animate-slide-in-left order-1 shadow-2xl border border-gray-600">
          {/* Prompt Input Section */}
          <div className="space-y-4">
            <div className="flex items-center gap-3 mb-1">
              <div className="p-2.5 bg-gradient-to-r from-purple-500 to-pink-500 rounded-xl shadow-lg">
                <Palette className="w-5 h-5 text-white" />
              </div>
              <h3 className="font-bold text-lg sm:text-xl bg-gradient-to-r from-white to-gray-200 bg-clip-text text-transparent">
                Describe Your Vision
              </h3>
            </div>
            <p className="text-gray-400 text-sm ml-12 mb-3">Transform your ideas into stunning AI artwork</p>

            <div className="space-y-3">
              <div className="relative group">
                <textarea
                  ref={textareaRef}
                  id="prompt"
                  value={prompt}
                  onChange={(e) => setPrompt(e.target.value)}
                  onKeyDown={handleKeyDown}
                  placeholder={placeholderExamples[placeholderIndex]}
                  className="w-full px-4 sm:px-5 py-4 sm:py-5 bg-gray-700/50 text-white
                           border-2 border-gray-500 rounded-xl resize-none text-base
                           focus:ring-2 focus:ring-purple-500 focus:border-purple-500 focus:bg-gray-700
                           placeholder-gray-400 transition-all duration-300 backdrop-blur-sm
                           placeholder:transition-all placeholder:duration-500 overflow-hidden
                           group-hover:border-gray-400"
                  style={{ minHeight: '140px', maxHeight: '300px' }}
                />

                {/* Clear Button Only */}
                {prompt && (
                  <div className="absolute top-3 right-3">
                    <button
                      onClick={() => setPrompt('')}
                      disabled={isLoading}
                      className="p-2.5 bg-gray-700 hover:bg-gray-600 rounded-lg transition-all duration-200
                               disabled:opacity-50 disabled:cursor-not-allowed hover:scale-110 shadow-lg"
                      title="Clear prompt"
                    >
                      <span className="w-4 h-4 text-white text-sm font-bold">×</span>
                    </button>
                  </div>
                )}
              </div>


            </div>


          </div>

          {/* Model Selection */}
          {/* <div className="space-y-3">
            <h3 className="text-white font-medium">Choose a model</h3>
            <div className="flex gap-2">
              <button className="px-4 py-2 bg-gray-700 text-gray-300 rounded-lg text-sm">
                Standard
              </button>
              <button className="px-4 py-2 bg-purple-600 text-white rounded-lg text-sm">
                HD
              </button>
              <button className="px-4 py-2 bg-gray-700 text-gray-300 rounded-lg text-sm">
                Genius
              </button>
            </div>
          </div> */}

          {/* Preference */}
          {/* <div className="space-y-3">
            <h3 className="text-white font-medium">Preference</h3>
            <div className="flex gap-2">
              <button className="px-4 py-2 bg-purple-600 text-white rounded-lg text-sm">
                Speed
              </button>
              <button className="px-4 py-2 bg-gray-700 text-gray-300 rounded-lg text-sm">
                Quality
              </button>
            </div>
          </div> */}

          {/* Enhanced Aspect Ratio Selector */}
          <div className="space-y-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-lg">
                <ImageIcon className="w-5 h-5 text-white" />
              </div>
              <h3 className="text-white font-semibold text-lg">Image Dimensions</h3>
            </div>

            <div className="grid grid-cols-2 sm:grid-cols-3 gap-3">
              {aspectRatios.map((ratio) => (
                <button
                  key={ratio.value}
                  onClick={() => setAspectRatio(ratio.value)}
                  disabled={isLoading}
                  className={`p-3 rounded-xl border-2 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed
                    ${aspectRatio === ratio.value
                      ? 'border-purple-500 bg-purple-500/20 text-purple-300'
                      : 'border-gray-600 bg-gray-800/50 text-gray-300 hover:border-gray-500 hover:bg-gray-700/50'
                    }`}
                >
                  <div className="text-center">
                    <div className={`mx-auto mb-2 rounded border-2 ${
                      aspectRatio === ratio.value ? 'border-purple-400' : 'border-gray-500'
                    }`} style={{
                      width: ratio.value === '1:1' ? '32px' :
                             ratio.value === '16:9' ? '40px' :
                             ratio.value === '9:16' ? '20px' :
                             ratio.value === '4:3' ? '36px' : '32px',
                      height: ratio.value === '1:1' ? '32px' :
                              ratio.value === '16:9' ? '22.5px' :
                              ratio.value === '9:16' ? '35.5px' :
                              ratio.value === '4:3' ? '27px' : '32px'
                    }}></div>
                    <div className="text-xs font-medium">{ratio.label}</div>
                  </div>
                </button>
              ))}
            </div>
          </div>

          {/* Enhanced Generate Button */}
          <div className="space-y-4">
            <button
              onClick={handleGenerate}
              disabled={isLoading || !prompt.trim()}
              className="w-full py-4 sm:py-5 bg-gradient-to-r from-purple-600 via-pink-600 to-orange-500
                       hover:from-purple-700 hover:via-pink-700 hover:to-orange-600
                       disabled:from-gray-500 disabled:via-gray-500 disabled:to-gray-500
                       text-white font-bold rounded-xl text-lg sm:text-xl
                       disabled:cursor-not-allowed flex items-center justify-center gap-3
                       transition-all duration-300 hover:scale-105 hover:shadow-2xl
                       animate-gradient-x disabled:animate-none shadow-lg
                       group relative overflow-hidden"
            >
              {/* Animated background */}
              <div className="absolute inset-0 bg-gradient-to-r from-purple-600 via-pink-600 to-orange-500 opacity-0 group-hover:opacity-20 transition-opacity duration-300"></div>

              {isLoading ? (
                <>
                  <ButtonSpinner />
                  <span>Creating Magic...</span>
                </>
              ) : (
                <>
                  <Wand2 className="w-5 h-5 sm:w-6 sm:h-6 transition-transform duration-200 group-hover:rotate-12" />
                  <span>Generate Art</span>
                  <ArrowRight className="w-5 h-5 sm:w-6 sm:h-6 transition-transform duration-200 group-hover:translate-x-1" />
                </>
              )}
            </button>

            {/* Generation Tips */}
            <div className="text-center text-sm text-gray-400">
              <p>💡 <span className="font-medium">Pro tip:</span> Be specific and descriptive for better results</p>
            </div>
          </div>
        </div>

        {/* Right Side - Enhanced Image Display */}
        <div className="w-full max-w-[800px] mx-auto bg-gradient-to-br from-gray-800 via-gray-700 to-gray-800 rounded-2xl p-6 sm:p-8 flex items-center justify-center min-h-[400px] sm:min-h-[500px] animate-slide-in-right order-2 shadow-2xl border border-gray-600">
          {isLoading ? (
            <div className="text-center space-y-6">
              <LoadingSpinner size="xl" text="Creating your masterpiece..." />
              <div className="space-y-3">
                <div className="flex justify-center gap-2">
                  <div className="w-2 h-2 bg-purple-500 rounded-full animate-bounce" style={{ animationDelay: '0ms' }}></div>
                  <div className="w-2 h-2 bg-pink-500 rounded-full animate-bounce" style={{ animationDelay: '150ms' }}></div>
                  <div className="w-2 h-2 bg-orange-500 rounded-full animate-bounce" style={{ animationDelay: '300ms' }}></div>
                </div>
                <p className="text-sm text-gray-400">AI is painting your vision...</p>
              </div>
            </div>
          ) : generatedImage ? (
            <div className="space-y-6 w-full animate-bounce-in">
              <div className="relative group">
                {/* Enhanced Image Container */}
                <div className="relative overflow-hidden rounded-2xl shadow-2xl bg-gradient-to-br from-purple-900 to-pink-900 p-2">
                  <img
                    src={generatedImage}
                    alt={lastPrompt}
                    className="w-full max-w-md lg:max-w-lg mx-auto rounded-xl shadow-lg
                             transition-all duration-500 hover:scale-105 relative z-10
                             animate-scale-in"
                    onError={() => setError('Failed to load generated image.')}
                  />

                  {/* Gradient Overlay on Hover */}
                  <div className="absolute inset-2 rounded-xl bg-gradient-to-t from-black/20 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 z-20"></div>
                </div>

                {/* Enhanced Action Buttons */}
                <div className="absolute top-4 right-4 flex gap-2 opacity-100 sm:opacity-0 sm:group-hover:opacity-100 transition-all duration-300 z-30">
                  <button
                    onClick={() => navigator.clipboard.writeText(lastPrompt)}
                    className="p-3 bg-white/95 backdrop-blur-sm hover:bg-white rounded-xl transition-all duration-200 shadow-lg hover:scale-110 group/btn"
                    title="Copy prompt"
                  >
                    <Copy className="w-5 h-5 text-gray-700 group-hover/btn:text-purple-600" />
                  </button>

                  <ShareButton
                    imageUrl={generatedImage}
                    prompt={lastPrompt}
                    onShare={handleShare}
                    className="p-3 bg-blue-600/95 backdrop-blur-sm hover:bg-blue-700 rounded-xl transition-all duration-200 shadow-lg hover:scale-110"
                  />

                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      handleDownloadClick();
                    }}
                    className="p-3 bg-green-600/95 backdrop-blur-sm hover:bg-green-700 rounded-xl transition-all duration-200 shadow-lg hover:scale-110 group/btn"
                    title="Download image"
                  >
                    <Download className="w-5 h-5 text-white" />
                  </button>
                </div>

                {/* Image Stats */}
                <div className="absolute bottom-4 left-4 flex gap-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300 z-30">
                  <div className="flex items-center gap-1 px-2 py-1 bg-black/50 backdrop-blur-sm rounded-lg text-white text-xs">
                    <Eye className="w-3 h-3" />
                    <span>AI Art</span>
                  </div>
                </div>
              </div>

              {/* Enhanced Prompt Display */}
              {lastPrompt && (
                <div className="bg-gradient-to-r from-gray-700 via-gray-600 to-gray-700 rounded-xl p-4 border border-gray-600">
                  <div className="flex items-start gap-3">
                    <div className="p-2 bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg flex-shrink-0">
                      <Palette className="w-4 h-4 text-white" />
                    </div>
                    <div className="flex-1">
                      <p className="text-sm font-medium text-gray-400 mb-1">Generated from:</p>
                      <p className="text-sm text-gray-300 leading-relaxed break-words">
                        "{lastPrompt}"
                      </p>
                    </div>
                  </div>
                </div>
              )}

              {/* ImageKit Upload Status */}
              {generatedImage && (
                <div className="bg-gradient-to-r from-blue-900/50 via-cyan-900/50 to-blue-900/50 rounded-xl p-4 border border-blue-600/30">
                  <div className="flex items-center gap-3">
                    {isUploadingToImageKit ? (
                      <>
                        <div className="p-2 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-lg flex-shrink-0 animate-pulse">
                          <Upload className="w-4 h-4 text-white" />
                        </div>
                        <div className="flex-1">
                          <p className="text-sm font-medium text-blue-400 mb-1">Uploading to secure storage...</p>
                          <p className="text-xs text-blue-300">Your image is being saved to our cloud storage</p>
                        </div>
                      </>
                    ) : imageKitUploadComplete && imageKitUrl ? (
                      <>
                        <div className="p-2 bg-gradient-to-r from-green-500 to-emerald-500 rounded-lg flex-shrink-0">
                          <CheckCircle className="w-4 h-4 text-white" />
                        </div>
                        <div className="flex-1">
                          <p className="text-sm font-medium text-green-400 mb-1">✅ Saved to secure storage!</p>
                          <p className="text-xs text-green-300">Your image is now safely stored and optimized</p>
                          {imageKitUrl && (
                            <div className="mt-2">
                              <p className="text-xs text-gray-400 mb-1">Optimized URL:</p>
                              <div className="flex items-center gap-2">
                                <code className="text-xs bg-gray-800 px-2 py-1 rounded text-cyan-400 break-all flex-1">
                                  {imageKitUrl.substring(0, 50)}...
                                </code>
                                <button
                                  onClick={() => {
                                    navigator.clipboard.writeText(imageKitUrl);
                                    // You could add a toast notification here
                                  }}
                                  className="p-1 bg-cyan-600 hover:bg-cyan-700 rounded text-white text-xs"
                                  title="Copy ImageKit URL"
                                >
                                  <Copy className="w-3 h-3" />
                                </button>
                              </div>
                            </div>
                          )}
                        </div>
                      </>
                    ) : (
                      <>
                        <div className="p-2 bg-gradient-to-r from-yellow-500 to-orange-500 rounded-lg flex-shrink-0">
                          <Upload className="w-4 h-4 text-white" />
                        </div>
                        <div className="flex-1">
                          <p className="text-sm font-medium text-yellow-400 mb-1">Processing for storage...</p>
                          <p className="text-xs text-yellow-300">Image will be saved to secure storage shortly</p>
                        </div>
                      </>
                    )}
                  </div>
                </div>
              )}
            </div>
          ) : (
            <div className="text-center space-y-6 animate-fade-in-up px-4">
              {/* Enhanced Empty State */}
              <div className="relative">
                <div className="w-32 h-32 sm:w-40 sm:h-40 mx-auto bg-gradient-to-br from-purple-900 via-pink-900 to-orange-900 rounded-3xl flex items-center justify-center animate-pulse-slow shadow-lg">
                  <ImageIcon className="w-16 h-16 sm:w-20 sm:h-20 text-purple-400 animate-float" />
                </div>
                <div className="absolute -top-2 -right-2 animate-bounce">
                  <Sparkles className="w-8 h-8 text-yellow-500" />
                </div>
              </div>

              <div className="space-y-4 animate-fade-in">
                <div className="text-center">
                  <h3 className="text-xl sm:text-2xl lg:text-3xl font-bold bg-gradient-to-r from-purple-600 via-pink-600 to-orange-500 bg-clip-text text-transparent mb-2">
                    Ready to Create Magic?
                  </h3>
                  <div className="w-16 h-1 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full mx-auto mb-3"></div>
                </div>
                <p className="text-base sm:text-lg text-gray-300 max-w-md mx-auto leading-relaxed text-center">
                  Describe your vision in the prompt box and watch AI bring it to life in stunning detail
                </p>

                {/* Feature Highlights */}
                <div className="grid grid-cols-2 gap-4 mt-6 max-w-sm mx-auto">
                  <div className="text-center p-3 bg-purple-900/20 rounded-xl">
                    <Zap className="w-6 h-6 text-purple-400 mx-auto mb-2" />
                    <p className="text-xs font-medium text-purple-300">Lightning Fast</p>
                  </div>
                  <div className="text-center p-3 bg-pink-900/20 rounded-xl">
                    <Star className="w-6 h-6 text-pink-400 mx-auto mb-2" />
                    <p className="text-xs font-medium text-pink-300">High Quality</p>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Enhanced Error Display */}
      {error && (
        <div className="mt-6 sm:mt-8 bg-gradient-to-r from-red-900/20 via-red-800/10 to-red-900/20
                      border-2 border-red-800 rounded-2xl p-4 sm:p-6 animate-slide-down shadow-lg">
          <div className="flex items-start gap-4">
            <div className="p-2 bg-red-900/50 rounded-xl flex-shrink-0">
              <AlertCircle className="w-6 h-6 text-red-400 animate-wiggle" />
            </div>
            <div className="flex-1">
              <h4 className="font-semibold text-red-300 text-lg mb-2">
                Oops! Something went wrong
              </h4>
              <p className="text-red-300 text-base leading-relaxed break-words animate-fade-in">
                {error}
              </p>
              <button
                onClick={() => setError(null)}
                className="mt-3 px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg text-sm font-medium transition-colors duration-200"
              >
                Dismiss
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Floating Background Elements */}
      <div className="fixed inset-0 pointer-events-none overflow-hidden -z-10">
        <div className="absolute top-20 left-10 w-32 h-32 bg-purple-800 rounded-full opacity-10 animate-float" style={{ animationDelay: '0s' }}></div>
        <div className="absolute top-40 right-20 w-24 h-24 bg-pink-800 rounded-full opacity-10 animate-float" style={{ animationDelay: '2s' }}></div>
        <div className="absolute bottom-40 left-20 w-20 h-20 bg-orange-800 rounded-full opacity-10 animate-float" style={{ animationDelay: '4s' }}></div>
        <div className="absolute bottom-20 right-10 w-28 h-28 bg-blue-800 rounded-full opacity-10 animate-float" style={{ animationDelay: '6s' }}></div>
      </div>
    </div>
  );
};

export default ImageGenerator;
