const aiservice = require("../services/ai.service");
const imagekitService = require("../services/imagekit.service");
const Image = require("../../models/Image");

const getResult = async (req, res) => {
try {
    const { prompt, aspectRatio } = req.body;

    // Step 1: Generate image using AI service
    const imageUrl = await aiservice(prompt, aspectRatio);

    if (!imageUrl) {
        return res.status(500).json({ error: 'Failed to generate image' });
    }

    // Step 2: Send the original imageUrl to frontend first (for immediate display)
    // This allows the frontend to show the image while we upload to ImageKit
    res.json({
        imageUrl: imageUrl,
        status: 'generated',
        message: 'Image generated successfully. Uploading to storage...'
    });

    // Step 3: Upload to ImageKit in the background
    const fileName = `ai-generated-${Date.now()}-${Math.random().toString(36).substring(7)}.jpg`;
    const imagekitResult = await imagekitService.uploadImageFromUrl(imageUrl, fileName);

    let imageKitUrl = '';
    let imageKitFileId = '';
    let imageKitFilePath = '';

    if (imagekitResult.success) {
        imageKitUrl = imagekitResult.imageKitUrl;
        imageKitFileId = imagekitResult.fileId;
        imageKitFilePath = imagekitResult.filePath;
        console.log('✅ Image uploaded to ImageKit:', imageKitUrl);
    } else {
        console.error('❌ Failed to upload to ImageKit:', imagekitResult.error);
    }

    // Step 4: Save to MongoDB with both URLs
    try {
        const newImage = new Image({
            prompt: prompt,
            imageUrl: imageUrl,
            imageKitUrl: imageKitUrl,
            imageKitFileId: imageKitFileId,
            imageKitFilePath: imageKitFilePath,
            aspectRatio: aspectRatio,
            userAgent: req.get('User-Agent') || '',
            ipAddress: req.ip || req.connection.remoteAddress || ''
        });

        await newImage.save();
        console.log('✅ Image saved to database with ImageKit URL');
    } catch (dbError) {
        console.error('❌ Failed to save image to database:', dbError.message);
    }

} catch (error) {
    console.error('❌ Error in getResult:', error);
    return res.status(500).json({ error: 'Failed to generate image' });
}
};

// Get ImageKit URL for a specific image
const getImageKitUrl = async (req, res) => {
    try {
        const { imageUrl } = req.query;

        if (!imageUrl) {
            return res.status(400).json({ error: 'Image URL is required' });
        }

        // Find the image in database by original URL
        const image = await Image.findOne({ imageUrl: imageUrl });

        if (!image) {
            return res.status(404).json({ error: 'Image not found' });
        }

        return res.json({
            success: true,
            imageKitUrl: image.imageKitUrl,
            imageKitFileId: image.imageKitFileId,
            imageKitFilePath: image.imageKitFilePath,
            hasImageKit: !!image.imageKitUrl
        });

    } catch (error) {
        console.error('❌ Error getting ImageKit URL:', error);
        return res.status(500).json({ error: 'Failed to get ImageKit URL' });
    }
};

module.exports = {
    getResult,
    getImageKitUrl,
};

