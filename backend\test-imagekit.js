require('dotenv').config();
const imagekitService = require('./src/services/imagekit.service');

async function testImageKitUpload() {
    console.log('🧪 Testing ImageKit integration...');
    
    // Test with a sample image URL (you can replace this with any public image URL)
    const testImageUrl = 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=800';
    const fileName = `test-upload-${Date.now()}.jpg`;
    
    try {
        console.log('📤 Uploading test image to ImageKit...');
        const result = await imagekitService.uploadImageFromUrl(testImageUrl, fileName);
        
        if (result.success) {
            console.log('✅ ImageKit upload successful!');
            console.log('📸 ImageKit URL:', result.imageKitUrl);
            console.log('🆔 File ID:', result.fileId);
            console.log('📁 File Path:', result.filePath);
            
            // Test getting image details
            console.log('\n🔍 Getting image details...');
            const detailsResult = await imagekitService.getImageDetails(result.fileId);
            
            if (detailsResult.success) {
                console.log('✅ Image details retrieved successfully');
                console.log('📊 File size:', detailsResult.details.size, 'bytes');
                console.log('📐 Dimensions:', detailsResult.details.width, 'x', detailsResult.details.height);
            } else {
                console.log('❌ Failed to get image details:', detailsResult.error);
            }
            
        } else {
            console.log('❌ ImageKit upload failed:', result.error);
        }
        
    } catch (error) {
        console.error('❌ Test failed:', error.message);
    }
}

// Check if ImageKit environment variables are set
console.log('🔧 Checking ImageKit configuration...');
console.log('Public Key:', process.env.IMAGEKIT_PUBLIC_KEY ? '✅ Set' : '❌ Missing');
console.log('Private Key:', process.env.IMAGEKIT_PRIVATE_KEY ? '✅ Set' : '❌ Missing');
console.log('URL Endpoint:', process.env.IMAGEKIT_URL_ENDPOINT ? '✅ Set' : '❌ Missing');

if (!process.env.IMAGEKIT_PUBLIC_KEY || !process.env.IMAGEKIT_PRIVATE_KEY || !process.env.IMAGEKIT_URL_ENDPOINT) {
    console.log('\n❌ ImageKit configuration is incomplete. Please set the following environment variables:');
    console.log('- IMAGEKIT_PUBLIC_KEY');
    console.log('- IMAGEKIT_PRIVATE_KEY');
    console.log('- IMAGEKIT_URL_ENDPOINT');
    console.log('\nYou can get these from your ImageKit dashboard: https://imagekit.io/dashboard');
    process.exit(1);
}

// Run the test
testImageKitUpload();
