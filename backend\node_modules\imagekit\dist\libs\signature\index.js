"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
/*
    Helper Modules
*/
var uuid_1 = require("uuid");
var crypto_1 = __importDefault(require("crypto"));
var DEFAULT_TIME_DIFF = 60 * 30;
var getAuthenticationParameters = function (token, expire, defaultOptions) {
    var defaultExpire = parseInt(String(new Date().getTime() / 1000), 10) + DEFAULT_TIME_DIFF;
    var authParameters = {
        token: token || "",
        expire: expire || 0,
        signature: "",
    };
    if (!defaultOptions || !defaultOptions.privateKey)
        return authParameters;
    token = token || (0, uuid_1.v4)();
    expire = expire || defaultExpire;
    var signature = crypto_1.default
        .createHmac("sha1", defaultOptions.privateKey)
        .update(token + expire)
        .digest("hex");
    authParameters.token = token;
    authParameters.expire = expire;
    authParameters.signature = signature;
    return authParameters;
};
exports.default = { getAuthenticationParameters: getAuthenticationParameters };
