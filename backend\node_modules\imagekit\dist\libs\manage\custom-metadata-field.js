"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
/*
    Constants
*/
var errorMessages_1 = __importDefault(require("../constants/errorMessages"));
/*
    Utils
*/
var respond_1 = __importDefault(require("../../utils/respond"));
var request_1 = __importDefault(require("../../utils/request"));
var create = function (createCustomMetadataFieldOptions, defaultOptions, callback) {
    var name = createCustomMetadataFieldOptions.name, label = createCustomMetadataFieldOptions.label, schema = createCustomMetadataFieldOptions.schema;
    if (!name || !name.length) {
        (0, respond_1.default)(true, errorMessages_1.default.CMF_NAME_MISSING, callback);
        return;
    }
    if (!label || !label.length) {
        (0, respond_1.default)(true, errorMessages_1.default.CMF_LABEL_MISSING, callback);
        return;
    }
    if (!schema) {
        (0, respond_1.default)(true, errorMessages_1.default.CMF_SCHEMA_MISSING, callback);
        return;
    }
    if (!schema.type) {
        (0, respond_1.default)(true, errorMessages_1.default.CMF_SCHEMA_INVALID, callback);
        return;
    }
    var requestOptions = {
        url: "https://api.imagekit.io/v1/customMetadataFields",
        method: "POST",
        json: {
            name: name,
            label: label,
            schema: schema
        },
    };
    (0, request_1.default)(requestOptions, defaultOptions, callback);
};
var list = function (getCustomMetadataFieldsOptions, defaultOptions, callback) {
    var _a = (getCustomMetadataFieldsOptions || {}).includeDeleted, includeDeleted = _a === void 0 ? false : _a;
    var requestOptions = {
        url: "https://api.imagekit.io/v1/customMetadataFields",
        method: "GET",
        qs: { includeDeleted: includeDeleted }
    };
    (0, request_1.default)(requestOptions, defaultOptions, callback);
};
var update = function (fieldId, updateCustomMetadataFieldOptions, defaultOptions, callback) {
    if (!fieldId || typeof fieldId !== "string" || !fieldId.length) {
        (0, respond_1.default)(true, errorMessages_1.default.CMF_FIELD_ID_MISSING, callback);
        return;
    }
    var label = updateCustomMetadataFieldOptions.label, schema = updateCustomMetadataFieldOptions.schema;
    if (!label && !schema) {
        (0, respond_1.default)(true, errorMessages_1.default.CMF_LABEL_SCHEMA_MISSING, callback);
        return;
    }
    var requestBody = {};
    if (label)
        requestBody.label = label;
    if (schema)
        requestBody.schema = schema;
    var requestOptions = {
        url: "https://api.imagekit.io/v1/customMetadataFields/".concat(fieldId),
        method: "PATCH",
        json: requestBody
    };
    (0, request_1.default)(requestOptions, defaultOptions, callback);
};
var deleteField = function (fieldId, defaultOptions, callback) {
    if (!fieldId || typeof fieldId !== "string" || !fieldId.length) {
        (0, respond_1.default)(true, errorMessages_1.default.CMF_FIELD_ID_MISSING, callback);
        return;
    }
    var requestOptions = {
        url: "https://api.imagekit.io/v1/customMetadataFields/".concat(fieldId),
        method: "DELETE",
    };
    (0, request_1.default)(requestOptions, defaultOptions, callback);
};
exports.default = { create: create, list: list, update: update, deleteField: deleteField };
