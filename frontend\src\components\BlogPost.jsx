import React from 'react';
import { ArrowLeft, Calendar, Clock, Tag, Share2 } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import SEO from './SEO';
import AdvertisementSpace from './AdvertisementSpace';

const BlogPost = ({ post, onBack }) => {
  const navigate = useNavigate();

  if (!post) {
    return (
      <div className="min-h-screen bg-gray-900 py-8">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center py-16">
            <h1 className="text-2xl font-bold text-white mb-4">Post Not Found</h1>
            <p className="text-gray-400 mb-8">The blog post you're looking for doesn't exist.</p>
            <button
              onClick={() => navigate('/')}
              className="inline-flex items-center gap-2 px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium transition-colors"
            >
              <ArrowLeft className="w-4 h-4" />
              Back to Home
            </button>
          </div>
        </div>
      </div>
    );
  }

  const handleShare = async () => {
    // Get the current URL which now includes the blog post slug
    const shareUrl = window.location.href;
    
    if (navigator.share) {
      try {
        await navigator.share({
          title: post.title,
          text: post.excerpt,
          url: shareUrl,
        });
      } catch (error) {
        console.log('Error sharing:', error);
      }
    } else {
      // Fallback: copy to clipboard
      navigator.clipboard.writeText(shareUrl);
      // You could show a toast notification here
      alert('Link copied to clipboard!');
    }
  };

  return (
    <div className="min-h-screen bg-gray-900 py-8">
      {/* SEO */}
      <SEO
        title={`${post.title} | Gen Free AI Blog`}
        description={post.excerpt}
        keywords={`${post.category}, AI image generation, ${post.title.toLowerCase()}`}
        url={window.location.href}
        type="article"
      />

      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Back Button */}
        <button
          onClick={onBack || (() => navigate('/blog'))}
          className="inline-flex items-center gap-2 text-blue-400 hover:text-blue-300 font-medium mb-8 transition-colors"
        >
          <ArrowLeft className="w-4 h-4" />
          Back to Blog
        </button>

        <div className="grid lg:grid-cols-4 gap-8">
          {/* Main Content */}
          <article className="lg:col-span-3">
            {/* Article Header */}
            <header className="mb-8">
              <div className="flex items-center gap-4 text-sm text-gray-400 mb-4">
                <span className="inline-flex items-center gap-1 px-3 py-1 rounded-full bg-blue-900 text-blue-200 font-medium">
                  <Tag className="w-3 h-3" />
                  {post.category}
                </span>
                <div className="flex items-center gap-1">
                  <Calendar className="w-4 h-4" />
                  {post.date}
                </div>
                <div className="flex items-center gap-1">
                  <Clock className="w-4 h-4" />
                  {post.readTime}
                </div>
              </div>

              <h1 className="text-3xl md:text-4xl font-bold text-white mb-4 leading-tight">
                {post.title}
              </h1>

              <p className="text-xl text-gray-300 leading-relaxed mb-6">
                {post.excerpt}
              </p>

              {/* Share Button */}
              <button
                onClick={handleShare}
                className="inline-flex items-center gap-2 px-4 py-2 bg-gray-800 hover:bg-gray-700 text-gray-300 rounded-lg font-medium transition-colors"
              >
                <Share2 className="w-4 h-4" />
                Share Article
              </button>
            </header>

            {/* Article Content */}
            <div
              className="prose prose-lg prose-invert max-w-none
                prose-headings:text-white
                prose-p:text-gray-300
                prose-li:text-gray-300
                prose-strong:text-white
                prose-a:text-blue-400
                prose-code:text-purple-400
                prose-pre:bg-gray-800
                prose-blockquote:border-blue-400"
              dangerouslySetInnerHTML={{ __html: post.content }}
            />

            {/* Article Footer */}
            <footer className="mt-12 pt-8 border-t border-gray-700">
              <div className="flex items-center justify-between">
                <div className="text-sm text-gray-400">
                  Published on {post.date}
                </div>
                <button
                  onClick={handleShare}
                  className="inline-flex items-center gap-2 text-blue-400 hover:text-blue-300 font-medium transition-colors"
                >
                  <Share2 className="w-4 h-4" />
                  Share this article
                </button>
              </div>
            </footer>
          </article>

          {/* Sidebar */}
          <aside className="lg:col-span-1 space-y-6">
            <div className="sticky top-8">
              <AdvertisementSpace />
              
              {/* Table of Contents (if needed) */}
              <div className="bg-gray-800 rounded-lg shadow-md p-6 mt-6">
                <h3 className="text-lg font-semibold text-white mb-4">
                  Quick Navigation
                </h3>
                <nav className="space-y-2">
                  <a href="#" className="block text-sm text-blue-400 hover:text-blue-300 transition-colors">
                    Understanding AI Prompts
                  </a>
                  <a href="#" className="block text-sm text-blue-400 hover:text-blue-300 transition-colors">
                    Style Keywords
                  </a>
                  <a href="#" className="block text-sm text-blue-400 hover:text-blue-300 transition-colors">
                    Composition Tips
                  </a>
                  <a href="#" className="block text-sm text-blue-400 hover:text-blue-300 transition-colors">
                    Quality Enhancers
                  </a>
                  <a href="#" className="block text-sm text-blue-400 hover:text-blue-300 transition-colors">
                    Examples
                  </a>
                </nav>
              </div>

              {/* Related Articles */}
              <div className="bg-gray-800 rounded-lg shadow-md p-6 mt-6">
                <h3 className="text-lg font-semibold text-white mb-4">
                  Related Articles
                </h3>
                <div className="space-y-3">
                  <a href="#" className="block group">
                    <h4 className="text-sm font-medium text-white group-hover:text-blue-400 transition-colors">
                      Advanced AI Techniques
                    </h4>
                    <p className="text-xs text-gray-400 mt-1">
                      Take your skills to the next level
                    </p>
                  </a>
                  <a href="#" className="block group">
                    <h4 className="text-sm font-medium text-white group-hover:text-blue-400 transition-colors">
                      Finding Reference Images
                    </h4>
                    <p className="text-xs text-gray-400 mt-1">
                      Best sources for inspiration
                    </p>
                  </a>
                </div>
              </div>
            </div>
          </aside>
        </div>
      </div>
    </div>
  );
};

export default BlogPost;
