const ImageKit = require('imagekit');
const axios = require('axios');

// Initialize ImageKit
const imagekit = new ImageKit({
    publicKey: process.env.IMAGEKIT_PUBLIC_KEY,
    privateKey: process.env.IMAGEKIT_PRIVATE_KEY,
    urlEndpoint: process.env.IMAGEKIT_URL_ENDPOINT
});

/**
 * Upload image from URL to ImageKit
 * @param {string} imageUrl - The URL of the image to upload
 * @param {string} fileName - The name for the uploaded file
 * @param {string} folder - The folder path in ImageKit (optional)
 * @returns {Promise<Object>} - ImageKit upload response
 */
const uploadImageFromUrl = async (imageUrl, fileName, folder = '/ai-generated') => {
    try {
        console.log('🔄 Uploading image to ImageKit...');
        
        // Download the image first
        const response = await axios.get(imageUrl, {
            responseType: 'arraybuffer',
            timeout: 30000 // 30 seconds timeout
        });
        
        const imageBuffer = Buffer.from(response.data);
        
        // Upload to ImageKit
        const uploadResponse = await imagekit.upload({
            file: imageBuffer,
            fileName: fileName,
            folder: folder,
            useUniqueFileName: true,
            tags: ['ai-generated', 'replicate'],
            transformation: {
                pre: 'l-text,i-GenFreeAI,fs-16,co-white,pa-10,g-south_east', // Add watermark
            }
        });
        
        console.log('✅ Image uploaded to ImageKit successfully');
        return {
            success: true,
            imageKitUrl: uploadResponse.url,
            fileId: uploadResponse.fileId,
            name: uploadResponse.name,
            size: uploadResponse.size,
            filePath: uploadResponse.filePath
        };
        
    } catch (error) {
        console.error('❌ ImageKit upload error:', error.message);
        return {
            success: false,
            error: error.message
        };
    }
};

/**
 * Delete image from ImageKit
 * @param {string} fileId - The ImageKit file ID
 * @returns {Promise<Object>} - Delete response
 */
const deleteImage = async (fileId) => {
    try {
        await imagekit.deleteFile(fileId);
        return { success: true };
    } catch (error) {
        console.error('❌ ImageKit delete error:', error.message);
        return { success: false, error: error.message };
    }
};

/**
 * Get image details from ImageKit
 * @param {string} fileId - The ImageKit file ID
 * @returns {Promise<Object>} - Image details
 */
const getImageDetails = async (fileId) => {
    try {
        const details = await imagekit.getFileDetails(fileId);
        return { success: true, details };
    } catch (error) {
        console.error('❌ ImageKit get details error:', error.message);
        return { success: false, error: error.message };
    }
};

/**
 * Generate optimized URL with transformations
 * @param {string} imagePath - The ImageKit file path
 * @param {Object} transformations - Transformation options
 * @returns {string} - Optimized image URL
 */
const getOptimizedUrl = (imagePath, transformations = {}) => {
    const defaultTransformations = {
        quality: 80,
        format: 'auto',
        ...transformations
    };
    
    return imagekit.url({
        path: imagePath,
        transformation: [defaultTransformations]
    });
};

module.exports = {
    uploadImageFromUrl,
    deleteImage,
    getImageDetails,
    getOptimizedUrl,
    imagekit
};
