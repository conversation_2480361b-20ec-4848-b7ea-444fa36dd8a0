"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
/*
    Helper Modules
*/
var lodash_1 = __importDefault(require("lodash"));
/*
    URL builder
*/
var builder_1 = __importDefault(require("./builder"));
function default_1(urlOpts, defaultOptions) {
    var opts = lodash_1.default.extend({}, defaultOptions, urlOpts);
    return builder_1.default.buildURL(opts);
}
exports.default = default_1;
