"use strict";
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
var lodash_1 = require("lodash");
/*
  Constants
*/
var errorMessages_1 = __importDefault(require("../constants/errorMessages"));
/*
    Utils
*/
var respond_1 = __importDefault(require("../../utils/respond"));
var request_1 = __importDefault(require("../../utils/request"));
/*
    Delete a file
*/
var deleteFile = function (fileId, defaultOptions, callback) {
    if (!fileId) {
        (0, respond_1.default)(true, errorMessages_1.default.FILE_ID_MISSING, callback);
        return;
    }
    var requestOptions = {
        url: "https://api.imagekit.io/v1/files/" + fileId,
        method: "DELETE"
    };
    (0, request_1.default)(requestOptions, defaultOptions, callback);
};
/*
    Delete a file version
*/
var deleteFileVersion = function (deleteFileVersionOptions, defaultOptions, callback) {
    var _a = deleteFileVersionOptions || {}, fileId = _a.fileId, versionId = _a.versionId;
    if (!fileId) {
        (0, respond_1.default)(true, errorMessages_1.default.FILE_ID_MISSING, callback);
        return;
    }
    if (!versionId) {
        (0, respond_1.default)(true, errorMessages_1.default.FILE_VERSION_ID_MISSING, callback);
        return;
    }
    var requestOptions = {
        url: "https://api.imagekit.io/v1/files/".concat(fileId, "/versions/").concat(versionId),
        method: "DELETE"
    };
    (0, request_1.default)(requestOptions, defaultOptions, callback);
};
/*
    Restore a file version as the current version
*/
var restoreFileVersion = function (restoreFileVersionOptions, defaultOptions, callback) {
    var _a = restoreFileVersionOptions || {}, fileId = _a.fileId, versionId = _a.versionId;
    if (!fileId) {
        (0, respond_1.default)(true, errorMessages_1.default.FILE_ID_MISSING, callback);
        return;
    }
    if (!versionId) {
        (0, respond_1.default)(true, errorMessages_1.default.FILE_VERSION_ID_MISSING, callback);
        return;
    }
    var requestOptions = {
        url: "https://api.imagekit.io/v1/files/".concat(fileId, "/versions/").concat(versionId, "/restore"),
        method: "PUT"
    };
    (0, request_1.default)(requestOptions, defaultOptions, callback);
};
/*
    Get Metadata of a file
*/
var getMetadata = function (fileIdOrURL, defaultOptions, callback) {
    if (!fileIdOrURL || fileIdOrURL.trim() == "") {
        (0, respond_1.default)(true, errorMessages_1.default.FILE_ID_OR_URL_MISSING, callback);
        return;
    }
    var requestOptions = {
        url: "https://api.imagekit.io/v1/files/" + fileIdOrURL + "/metadata",
        method: "GET"
    };
    // In case of URL change the endopint
    if (fileIdOrURL.indexOf("http") === 0) {
        requestOptions = {
            url: "https://api.imagekit.io/v1/metadata?url=".concat(fileIdOrURL),
            method: "GET"
        };
    }
    (0, request_1.default)(requestOptions, defaultOptions, callback);
};
/*
    Get Details of a file
*/
var getDetails = function (fileId, defaultOptions, callback) {
    if (!fileId) {
        (0, respond_1.default)(true, errorMessages_1.default.FILE_ID_MISSING, callback);
        return;
    }
    var requestOptions = {
        url: "https://api.imagekit.io/v1/files/" + fileId + "/details",
        method: "GET"
    };
    (0, request_1.default)(requestOptions, defaultOptions, callback);
};
/*
    Get Details of a file version
*/
var getFileVersionDetails = function (fileDetailsOptions, defaultOptions, callback) {
    var _a = fileDetailsOptions || {}, fileId = _a.fileId, versionId = _a.versionId;
    if (!fileId) {
        (0, respond_1.default)(true, errorMessages_1.default.FILE_ID_MISSING, callback);
        return;
    }
    if (!versionId) {
        (0, respond_1.default)(true, errorMessages_1.default.FILE_VERSION_ID_MISSING, callback);
        return;
    }
    var requestOptions = {
        url: "https://api.imagekit.io/v1/files/".concat(fileId, "/versions/").concat(versionId),
        method: "GET"
    };
    (0, request_1.default)(requestOptions, defaultOptions, callback);
};
/*
    Update file details
*/
var updateDetails = function (fileId, updateData, defaultOptions, callback) {
    if (!fileId) {
        (0, respond_1.default)(true, errorMessages_1.default.FILE_ID_MISSING, callback);
        return;
    }
    if (!(0, lodash_1.isObject)(updateData)) {
        (0, respond_1.default)(true, errorMessages_1.default.UPDATE_DATA_MISSING, callback);
        return;
    }
    var data = {};
    data = {
        tags: updateData.tags,
        customCoordinates: updateData.customCoordinates,
        extensions: updateData.extensions,
        webhookUrl: updateData.webhookUrl,
        customMetadata: updateData.customMetadata,
    };
    if (updateData.publish)
        data = __assign(__assign({}, data), { publish: updateData.publish });
    var requestOptions = {
        url: "https://api.imagekit.io/v1/files/" + fileId + "/details",
        method: "PATCH",
        json: data,
    };
    (0, request_1.default)(requestOptions, defaultOptions, callback);
};
/*
    List files
*/
var listFiles = function (listOptions, defaultOptions, callback) {
    if (listOptions && !(0, lodash_1.isObject)(listOptions)) {
        (0, respond_1.default)(true, errorMessages_1.default.INVALID_LIST_OPTIONS, callback);
        return;
    }
    if (listOptions && listOptions.tags && Array.isArray(listOptions.tags) && listOptions.tags.length) {
        listOptions.tags = listOptions.tags.join(",");
    }
    var requestOptions = {
        url: "https://api.imagekit.io/v1/files/",
        method: "GET",
        qs: listOptions || {}
    };
    (0, request_1.default)(requestOptions, defaultOptions, callback);
};
/*
    Get all versions of an asset
*/
var getFilesVersions = function (fileId, defaultOptions, callback) {
    if (!fileId) {
        (0, respond_1.default)(true, errorMessages_1.default.FILE_ID_MISSING, callback);
        return;
    }
    var requestOptions = {
        url: "https://api.imagekit.io/v1/files/".concat(fileId, "/versions"),
        method: "GET"
    };
    (0, request_1.default)(requestOptions, defaultOptions, callback);
};
/*
    Bulk Delete By FileIds
*/
var bulkDeleteFiles = function (fileIdArray, defaultOptions, callback) {
    if (!Array.isArray(fileIdArray) ||
        fileIdArray.length === 0 ||
        fileIdArray.filter(function (fileId) { return typeof fileId !== "string"; }).length > 0) {
        (0, respond_1.default)(true, errorMessages_1.default.INVALID_FILEIDS_VALUE, callback);
        return;
    }
    var data = {
        fileIds: fileIdArray,
    };
    var requestOptions = {
        url: "https://api.imagekit.io/v1/files/batch/deleteByFileIds",
        method: "POST",
        json: data,
    };
    (0, request_1.default)(requestOptions, defaultOptions, callback);
};
/*
    Add tags in bulk
*/
var bulkAddTags = function (fileIdArray, tags, defaultOptions, callback) {
    if (!Array.isArray(fileIdArray) ||
        fileIdArray.length === 0 ||
        fileIdArray.filter(function (fileId) { return typeof fileId !== "string"; }).length > 0) {
        (0, respond_1.default)(true, errorMessages_1.default.INVALID_FILEIDS_VALUE, callback);
        return;
    }
    if (!Array.isArray(tags) || tags.length === 0 || tags.filter(function (tag) { return typeof tag !== "string"; }).length > 0) {
        (0, respond_1.default)(true, errorMessages_1.default.BULK_ADD_TAGS_INVALID, callback);
        return;
    }
    var data = {
        fileIds: fileIdArray,
        tags: tags,
    };
    var requestOptions = {
        url: "https://api.imagekit.io/v1/files/addTags",
        method: "POST",
        json: data,
    };
    (0, request_1.default)(requestOptions, defaultOptions, callback);
};
/*
    Remove tags in bulk
*/
var bulkRemoveTags = function (fileIdArray, tags, defaultOptions, callback) {
    if (!Array.isArray(fileIdArray) ||
        fileIdArray.length === 0 ||
        fileIdArray.filter(function (fileId) { return typeof fileId !== "string"; }).length > 0) {
        (0, respond_1.default)(true, errorMessages_1.default.INVALID_FILEIDS_VALUE, callback);
        return;
    }
    if (!Array.isArray(tags) || tags.length === 0 || tags.filter(function (tag) { return typeof tag !== "string"; }).length > 0) {
        (0, respond_1.default)(true, errorMessages_1.default.BULK_ADD_TAGS_INVALID, callback);
        return;
    }
    var data = {
        fileIds: fileIdArray,
        tags: tags,
    };
    var requestOptions = {
        url: "https://api.imagekit.io/v1/files/removeTags",
        method: "POST",
        json: data,
    };
    (0, request_1.default)(requestOptions, defaultOptions, callback);
};
/*
    Remove AI tags in bulk
*/
var bulkRemoveAITags = function (fileIdArray, tags, defaultOptions, callback) {
    if (!Array.isArray(fileIdArray) ||
        fileIdArray.length === 0 ||
        fileIdArray.filter(function (fileId) { return typeof fileId !== "string"; }).length > 0) {
        (0, respond_1.default)(true, errorMessages_1.default.INVALID_FILEIDS_VALUE, callback);
        return;
    }
    if (!Array.isArray(tags) || tags.length === 0 || tags.filter(function (tag) { return typeof tag !== "string"; }).length > 0) {
        (0, respond_1.default)(true, errorMessages_1.default.BULK_ADD_TAGS_INVALID, callback);
        return;
    }
    var data = {
        fileIds: fileIdArray,
        AITags: tags,
    };
    var requestOptions = {
        url: "https://api.imagekit.io/v1/files/removeAITags",
        method: "POST",
        json: data,
    };
    (0, request_1.default)(requestOptions, defaultOptions, callback);
};
/*
    Copy file
*/
var copyFile = function (copyFileOptions, defaultOptions, callback) {
    var sourceFilePath = copyFileOptions.sourceFilePath, destinationPath = copyFileOptions.destinationPath, _a = copyFileOptions.includeFileVersions, includeFileVersions = _a === void 0 ? false : _a;
    if (typeof sourceFilePath !== "string" || sourceFilePath.length === 0) {
        (0, respond_1.default)(true, errorMessages_1.default.INVALID_SOURCE_FILE_PATH, callback);
        return;
    }
    if (typeof destinationPath !== "string" || destinationPath.length === 0) {
        (0, respond_1.default)(true, errorMessages_1.default.INVALID_DESTINATION_FOLDER_PATH, callback);
        return;
    }
    if (typeof includeFileVersions !== "boolean") {
        (0, respond_1.default)(true, errorMessages_1.default.INVALID_INCLUDE_VERSION, callback);
        return;
    }
    var data = {
        sourceFilePath: sourceFilePath,
        destinationPath: destinationPath,
        includeFileVersions: includeFileVersions
    };
    var requestOptions = {
        url: "https://api.imagekit.io/v1/files/copy",
        method: "POST",
        json: data,
    };
    (0, request_1.default)(requestOptions, defaultOptions, callback);
};
/*
    Move file
*/
var moveFile = function (moveFileOptions, defaultOptions, callback) {
    var sourceFilePath = moveFileOptions.sourceFilePath, destinationPath = moveFileOptions.destinationPath;
    if (typeof sourceFilePath !== "string" || sourceFilePath.length === 0) {
        (0, respond_1.default)(true, errorMessages_1.default.INVALID_SOURCE_FILE_PATH, callback);
        return;
    }
    if (typeof destinationPath !== "string" || destinationPath.length === 0) {
        (0, respond_1.default)(true, errorMessages_1.default.INVALID_DESTINATION_FOLDER_PATH, callback);
        return;
    }
    var data = {
        sourceFilePath: sourceFilePath,
        destinationPath: destinationPath
    };
    var requestOptions = {
        url: "https://api.imagekit.io/v1/files/move",
        method: "POST",
        json: data,
    };
    (0, request_1.default)(requestOptions, defaultOptions, callback);
};
/*
    Rename file
*/
var renameFile = function (renameFileOptions, defaultOptions, callback) {
    var filePath = renameFileOptions.filePath, newFileName = renameFileOptions.newFileName, _a = renameFileOptions.purgeCache, purgeCache = _a === void 0 ? false : _a;
    if (typeof filePath !== "string" || filePath.length === 0) {
        (0, respond_1.default)(true, errorMessages_1.default.INVALID_FILE_PATH, callback);
        return;
    }
    if (typeof newFileName !== "string" || newFileName.length === 0) {
        (0, respond_1.default)(true, errorMessages_1.default.INVALID_NEW_FILE_NAME, callback);
        return;
    }
    if (typeof purgeCache !== "boolean") {
        (0, respond_1.default)(true, errorMessages_1.default.INVALID_PURGE_CACHE, callback);
        return;
    }
    var data = {
        filePath: filePath,
        newFileName: newFileName,
        purgeCache: purgeCache
    };
    var requestOptions = {
        url: "https://api.imagekit.io/v1/files/rename",
        method: "PUT",
        json: data,
    };
    (0, request_1.default)(requestOptions, defaultOptions, callback);
};
/*
    Copy Folder
*/
var copyFolder = function (copyFolderOptions, defaultOptions, callback) {
    var sourceFolderPath = copyFolderOptions.sourceFolderPath, destinationPath = copyFolderOptions.destinationPath, _a = copyFolderOptions.includeFileVersions, includeFileVersions = _a === void 0 ? false : _a;
    if (typeof sourceFolderPath !== "string" || sourceFolderPath.length === 0) {
        (0, respond_1.default)(true, errorMessages_1.default.INVALID_SOURCE_FOLDER_PATH, callback);
        return;
    }
    if (typeof destinationPath !== "string" || destinationPath.length === 0) {
        (0, respond_1.default)(true, errorMessages_1.default.INVALID_DESTINATION_FOLDER_PATH, callback);
        return;
    }
    if (typeof includeFileVersions !== "boolean") {
        (0, respond_1.default)(true, errorMessages_1.default.INVALID_INCLUDE_VERSION, callback);
        return;
    }
    var data = {
        sourceFolderPath: sourceFolderPath,
        destinationPath: destinationPath,
        includeFileVersions: includeFileVersions
    };
    var requestOptions = {
        url: "https://api.imagekit.io/v1/bulkJobs/copyFolder",
        method: "POST",
        json: data,
    };
    (0, request_1.default)(requestOptions, defaultOptions, callback);
};
/*
    Move Folder
*/
var moveFolder = function (moveFolderOptions, defaultOptions, callback) {
    var sourceFolderPath = moveFolderOptions.sourceFolderPath, destinationPath = moveFolderOptions.destinationPath;
    if (typeof sourceFolderPath !== "string" || sourceFolderPath.length === 0) {
        (0, respond_1.default)(true, errorMessages_1.default.INVALID_SOURCE_FOLDER_PATH, callback);
        return;
    }
    if (typeof destinationPath !== "string" || destinationPath.length === 0) {
        (0, respond_1.default)(true, errorMessages_1.default.INVALID_DESTINATION_FOLDER_PATH, callback);
        return;
    }
    var data = {
        sourceFolderPath: sourceFolderPath,
        destinationPath: destinationPath,
    };
    var requestOptions = {
        url: "https://api.imagekit.io/v1/bulkJobs/moveFolder",
        method: "POST",
        json: data,
    };
    (0, request_1.default)(requestOptions, defaultOptions, callback);
};
/*
    Create folder
*/
var createFolder = function (createFolderOptions, defaultOptions, callback) {
    var folderName = createFolderOptions.folderName, parentFolderPath = createFolderOptions.parentFolderPath;
    if (typeof folderName !== "string" || folderName.length === 0) {
        (0, respond_1.default)(true, errorMessages_1.default.INVALID_FOLDER_NAME, callback);
        return;
    }
    if (typeof parentFolderPath !== "string" || parentFolderPath.length === 0) {
        (0, respond_1.default)(true, errorMessages_1.default.INVALID_PARENT_FOLDER_PATH, callback);
        return;
    }
    var data = {
        folderName: folderName,
        parentFolderPath: parentFolderPath,
    };
    var requestOptions = {
        url: "https://api.imagekit.io/v1/folder",
        method: "POST",
        json: data,
    };
    (0, request_1.default)(requestOptions, defaultOptions, callback);
};
/*
    Delete folder
*/
var deleteFolder = function (folderPath, defaultOptions, callback) {
    if (typeof folderPath !== "string" || folderPath.length === 0) {
        (0, respond_1.default)(true, errorMessages_1.default.INVALID_FOLDER_PATH, callback);
        return;
    }
    var data = {
        folderPath: folderPath,
    };
    var requestOptions = {
        url: "https://api.imagekit.io/v1/folder",
        method: "DELETE",
        json: data,
    };
    (0, request_1.default)(requestOptions, defaultOptions, callback);
};
/*
    Bulk job status
*/
var getBulkJobStatus = function (jobId, defaultOptions, callback) {
    if (!jobId) {
        (0, respond_1.default)(true, errorMessages_1.default.JOB_ID_MISSING, callback);
        return;
    }
    var requestOptions = {
        url: "https://api.imagekit.io/v1/bulkJobs/" + jobId,
        method: "GET"
    };
    (0, request_1.default)(requestOptions, defaultOptions, callback);
};
exports.default = {
    deleteFile: deleteFile,
    getMetadata: getMetadata,
    getDetails: getDetails,
    getFileVersionDetails: getFileVersionDetails,
    updateDetails: updateDetails,
    listFiles: listFiles,
    getFilesVersions: getFilesVersions,
    bulkDeleteFiles: bulkDeleteFiles,
    deleteFileVersion: deleteFileVersion,
    restoreFileVersion: restoreFileVersion,
    bulkAddTags: bulkAddTags,
    bulkRemoveTags: bulkRemoveTags,
    bulkRemoveAITags: bulkRemoveAITags,
    copyFile: copyFile,
    moveFile: moveFile,
    renameFile: renameFile,
    copyFolder: copyFolder,
    moveFolder: moveFolder,
    createFolder: createFolder,
    deleteFolder: deleteFolder,
    getBulkJobStatus: getBulkJobStatus,
};
