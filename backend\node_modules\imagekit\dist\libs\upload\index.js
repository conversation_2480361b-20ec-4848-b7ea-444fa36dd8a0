"use strict";
var __values = (this && this.__values) || function(o) {
    var s = typeof Symbol === "function" && Symbol.iterator, m = s && o[s], i = 0;
    if (m) return m.call(o);
    if (o && typeof o.length === "number") return {
        next: function () {
            if (o && i >= o.length) o = void 0;
            return { value: o && o[i++], done: !o };
        }
    };
    throw new TypeError(s ? "Object is not iterable." : "Symbol.iterator is not defined.");
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
var lodash_1 = __importDefault(require("lodash"));
var errorMessages_1 = __importDefault(require("../constants/errorMessages"));
var respond_1 = __importDefault(require("../../utils/respond"));
var request_1 = __importDefault(require("../../utils/request"));
var form_data_1 = __importDefault(require("form-data"));
function default_1(uploadOptions, defaultOptions, callback) {
    var e_1, _a;
    if (!lodash_1.default.isObject(uploadOptions)) {
        (0, respond_1.default)(true, errorMessages_1.default.MISSING_UPLOAD_DATA, callback);
        return;
    }
    if (!uploadOptions.file) {
        (0, respond_1.default)(true, errorMessages_1.default.MISSING_UPLOAD_FILE_PARAMETER, callback);
        return;
    }
    if (!uploadOptions.fileName) {
        (0, respond_1.default)(true, errorMessages_1.default.MISSING_UPLOAD_FILENAME_PARAMETER, callback);
        return;
    }
    if (uploadOptions.transformation) {
        if (!(Object.keys(uploadOptions.transformation).includes("pre") || Object.keys(uploadOptions.transformation).includes("post"))) {
            (0, respond_1.default)(true, errorMessages_1.default.INVALID_TRANSFORMATION, callback);
            return;
        }
        if (Object.keys(uploadOptions.transformation).includes("pre") && !uploadOptions.transformation.pre) {
            (0, respond_1.default)(true, errorMessages_1.default.INVALID_PRE_TRANSFORMATION, callback);
            return;
        }
        if (Object.keys(uploadOptions.transformation).includes("post")) {
            if (Array.isArray(uploadOptions.transformation.post)) {
                try {
                    for (var _b = __values(uploadOptions.transformation.post), _c = _b.next(); !_c.done; _c = _b.next()) {
                        var transformation = _c.value;
                        if (transformation.type === "abs" && !(transformation.protocol || transformation.value)) {
                            (0, respond_1.default)(true, errorMessages_1.default.INVALID_POST_TRANSFORMATION, callback);
                            return;
                        }
                        else if (transformation.type === "transformation" && !transformation.value) {
                            (0, respond_1.default)(true, errorMessages_1.default.INVALID_POST_TRANSFORMATION, callback);
                            return;
                        }
                    }
                }
                catch (e_1_1) { e_1 = { error: e_1_1 }; }
                finally {
                    try {
                        if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
                    }
                    finally { if (e_1) throw e_1.error; }
                }
            }
            else {
                (0, respond_1.default)(true, errorMessages_1.default.INVALID_POST_TRANSFORMATION, callback);
                return;
            }
        }
    }
    var formData = {};
    var form = new form_data_1.default();
    var key;
    for (key in uploadOptions) {
        if (key) {
            if (key == "file" && typeof uploadOptions.file != "string") {
                // form.append('file', uploadOptions.file);
                form.append('file', uploadOptions.file, String(uploadOptions.fileName));
            }
            else if (key == "tags" && Array.isArray(uploadOptions.tags)) {
                form.append('tags', uploadOptions.tags.join(","));
            }
            else if (key == "responseFields" && Array.isArray(uploadOptions.responseFields)) {
                form.append('responseFields', uploadOptions.responseFields.join(","));
            }
            else if (key == "extensions" && Array.isArray(uploadOptions.extensions)) {
                form.append('extensions', JSON.stringify(uploadOptions.extensions));
            }
            else if (key === "customMetadata" && typeof uploadOptions.customMetadata === "object" &&
                !Array.isArray(uploadOptions.customMetadata) && uploadOptions.customMetadata !== null) {
                form.append('customMetadata', JSON.stringify(uploadOptions.customMetadata));
            }
            else if (key === "transformation" && typeof uploadOptions.transformation === "object" &&
                uploadOptions.transformation !== null) {
                form.append(key, JSON.stringify(uploadOptions.transformation));
            }
            else if (key === "checks" && uploadOptions.checks) {
                form.append(key, uploadOptions.checks);
            }
            else {
                form.append(key, String(uploadOptions[key]));
            }
        }
    }
    var requestOptions = {
        url: defaultOptions.uploadEndpoint || "https://upload.imagekit.io/api/v1/files/upload",
        method: "POST",
        formData: form
    };
    (0, request_1.default)(requestOptions, defaultOptions, callback);
}
exports.default = default_1;
