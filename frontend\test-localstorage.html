<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LocalStorage Test</title>
</head>
<body>
    <h1>LocalStorage Test</h1>
    <button onclick="checkLocalStorage()">Check LocalStorage</button>
    <button onclick="addTestImage()">Add Test Image</button>
    <button onclick="clearLocalStorage()">Clear LocalStorage</button>
    <div id="output"></div>

    <script>
        function checkLocalStorage() {
            const images = JSON.parse(localStorage.getItem('generatedImages') || '[]');
            document.getElementById('output').innerHTML = `
                <h2>LocalStorage Contents:</h2>
                <p>Found ${images.length} images</p>
                <pre>${JSON.stringify(images, null, 2)}</pre>
            `;
        }

        function addTestImage() {
            const testImage = {
                imageUrl: 'https://ik.imagekit.io/q0mafimea/test-image.jpg',
                prompt: 'Test image for debugging',
                aspectRatio: '1:1',
                timestamp: new Date().toISOString(),
                isImageKit: true
            };

            const existingImages = JSON.parse(localStorage.getItem('generatedImages') || '[]');
            existingImages.unshift(testImage);
            localStorage.setItem('generatedImages', JSON.stringify(existingImages));
            
            checkLocalStorage();
        }

        function clearLocalStorage() {
            localStorage.removeItem('generatedImages');
            localStorage.removeItem('ai_image_history'); // Remove old key too
            checkLocalStorage();
        }

        // Check on page load
        checkLocalStorage();
    </script>
</body>
</html>
