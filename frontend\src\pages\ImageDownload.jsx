import React, { useState, useEffect } from 'react';
import { Download, ArrowLeft, Share2, Heart, Star, Copy, <PERSON>rkles, Eye, Palette, Zap, CheckCircle, ExternalLink, Image as ImageIcon, Info, Shield, Clock, Users, Globe, Award, Bookmark, Camera } from 'lucide-react';
import { useSearchParams, useNavigate } from 'react-router-dom';
import AdvertisementSpace from '../components/AdvertisementSpace';

const ImageDownload = () => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const [isDownloading, setIsDownloading] = useState(false);
  const [downloadComplete, setDownloadComplete] = useState(false);
  const [imageSize, setImageSize] = useState('Loading...');
  const [imageDimensions, setImageDimensions] = useState({ width: 0, height: 0 });

  const imageUrl = searchParams.get('url');
  const prompt = searchParams.get('prompt') || 'Generated Image';

  // Function to get image dimensions
  const getImageDimensions = (url) => {
    return new Promise((resolve, reject) => {
      const img = new Image();
      img.onload = () => {
        resolve({ width: img.naturalWidth, height: img.naturalHeight });
      };
      img.onerror = () => {
        reject(new Error('Failed to load image'));
      };
      img.src = url;
    });
  };

  useEffect(() => {
    // If no image URL, redirect back
    if (!imageUrl) {
      navigate('/');
    } else {
      // Scroll to top when page loads
      window.scrollTo({ top: 0, behavior: 'smooth' });

      // Get image dimensions
      getImageDimensions(imageUrl)
        .then((dimensions) => {
          setImageDimensions(dimensions);
          setImageSize(`${dimensions.width}×${dimensions.height}`);
        })
        .catch((error) => {
          console.error('Error getting image dimensions:', error);
          setImageSize('Unknown');
          setImageDimensions({ width: 0, height: 0 });
        });
    }
  }, [imageUrl, navigate]);

  const handleDownload = async () => {
    if (!imageUrl) {
      console.error('No image URL provided');
      alert('❌ No image URL found. Please try again.');
      return;
    }

    // Starting download process
    setIsDownloading(true);

    try {
      // Method 1: Try fetch with different CORS modes
      let response;
      try {
        response = await fetch(imageUrl, { mode: 'cors' });
      } catch (corsErr) {
        // CORS failed, trying no-cors mode
        response = await fetch(imageUrl, { mode: 'no-cors' });
      }

      if (response.ok || response.type === 'opaque') {
        const blob = await response.blob();

        // Only proceed if we got a valid blob
        if (blob && blob.size > 0) {
          const url = window.URL.createObjectURL(blob);
          const link = document.createElement('a');
          link.href = url;
          link.download = `ai-generated-image-${Date.now()}.JPG`;
          link.style.display = 'none';

          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
          window.URL.revokeObjectURL(url);

          setDownloadComplete(true);
          setTimeout(() => setDownloadComplete(false), 3000);
          return;
        }
      }

      throw new Error('Failed to fetch image blob');

    } catch (fetchErr) {
      console.error('Fetch download failed:', fetchErr);

      // Method 2: Try direct link download
      try {
        const link = document.createElement('a');
        link.href = imageUrl;
        link.download = `ai-generated-image-${Date.now()}.JPG`;
        link.target = '_blank';
        link.rel = 'noopener noreferrer';
        link.style.display = 'none';

        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        setDownloadComplete(true);
        setTimeout(() => setDownloadComplete(false), 3000);
        return;

      } catch (directErr) {
        console.error('Direct download failed:', directErr);

        // Method 3: Force download using window.open
        try {
          const newWindow = window.open(imageUrl, '_blank');
          if (newWindow) {
            setDownloadComplete(true);
            setTimeout(() => setDownloadComplete(false), 3000);
            alert('✅ Image opened in new tab. Right-click and select "Save image as..." to download.');
            return;
          }
        } catch (windowErr) {
          console.error('Window open failed:', windowErr);
        }

        // Method 4: Final fallback - copy URL and show instructions
        try {
          await navigator.clipboard.writeText(imageUrl);
          alert('❌ Download failed. Image URL copied to clipboard.\n\nTo download:\n1. Paste the URL in a new tab\n2. Right-click the image\n3. Select "Save image as..."');
        } catch (clipboardErr) {
          alert(`❌ Download failed. Please copy this URL and open it in a new tab:\n\n${imageUrl}\n\nThen right-click and select "Save image as..."`);
        }
      }
    } finally {
      setIsDownloading(false);
    }
  };

  const handleShare = async () => {
    try {
      // Method 1: Try to share the actual image file (works on mobile)
      if (navigator.share && navigator.canShare) {
        const response = await fetch(imageUrl, { mode: 'cors' });
        const blob = await response.blob();
        const file = new File([blob], `ai-generated-image-${Date.now()}.JPG`, {
          type: blob.type || 'image/JPG'
        });

        // Check if we can share files
        if (navigator.canShare({ files: [file] })) {
          await navigator.share({
            title: 'AI Generated Image',
            text: `Amazing AI art: "${prompt}"`,
            files: [file],
          });
          return;
        }
      }

      // Method 2: Copy image to clipboard (works on desktop)
      if (navigator.clipboard && navigator.clipboard.write) {
        try {
          const response = await fetch(imageUrl, { mode: 'cors' });
          const blob = await response.blob();
          const clipboardItem = new ClipboardItem({ [blob.type]: blob });

          await navigator.clipboard.write([clipboardItem]);
          alert('✅ Image copied to clipboard! You can now paste it anywhere.');
          return;
        } catch (clipboardErr) {
          console.log('Clipboard image copy failed:', clipboardErr);
        }
      }

      // Method 3: Fallback to URL sharing
      if (navigator.share) {
        await navigator.share({
          title: 'AI Generated Image',
          text: `Check out this AI-generated image: "${prompt}"`,
          url: imageUrl,
        });
        return;
      }

      // Method 4: Copy URL to clipboard
      await navigator.clipboard.writeText(imageUrl);
      alert('📋 Image URL copied to clipboard!');

    } catch (err) {
      console.error('Share error:', err);

      // Final fallback: Manual copy
      try {
        await navigator.clipboard.writeText(imageUrl);
        alert('📋 Image URL copied to clipboard!');
      } catch (finalErr) {
        window.prompt(`Copy this image URL to share:\n\n${imageUrl}`);
      }
    }
  };

  const handleGoBack = () => {
    navigate(-1);
  };

  const handleTermsClick = () => {
    navigate('/terms');
  };



  if (!imageUrl) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-900 via-purple-900/20 to-gray-900 flex items-center justify-center relative overflow-hidden">
        {/* Animated Background Elements */}
        <div className="absolute inset-0 pointer-events-none">
          <div className="absolute top-20 left-10 w-32 h-32 bg-purple-800 rounded-full opacity-20 animate-pulse"></div>
          <div className="absolute bottom-20 right-10 w-24 h-24 bg-blue-800 rounded-full opacity-20 animate-pulse" style={{ animationDelay: '1s' }}></div>
          <div className="absolute top-1/2 left-1/4 w-16 h-16 bg-indigo-800 rounded-full opacity-20 animate-pulse" style={{ animationDelay: '2s' }}></div>
        </div>

        <div className="text-center relative z-10 max-w-md mx-auto px-6">
          <div className="relative inline-block mb-8">
            <div className="w-24 h-24 bg-gradient-to-br from-purple-500 to-blue-600 rounded-3xl flex items-center justify-center shadow-2xl transform rotate-12 hover:rotate-0 transition-transform duration-500">
              <ImageIcon className="w-12 h-12 text-white" />
            </div>
            <div className="absolute -top-2 -right-2">
              <Sparkles className="w-8 h-8 text-yellow-400 animate-bounce" />
            </div>
          </div>

          <h2 className="text-4xl font-bold bg-gradient-to-r from-purple-600 via-blue-600 to-indigo-600 bg-clip-text text-transparent mb-4">
            Oops! Image Not Found
          </h2>
          <p className="text-lg text-gray-300 mb-8 leading-relaxed">
            The masterpiece you're looking for seems to have vanished into the digital void. Let's create something new!
          </p>

          <button
            onClick={() => navigate('/')}
            className="group inline-flex items-center gap-3 px-8 py-4 bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white font-semibold rounded-2xl transition-all duration-300 shadow-xl hover:shadow-2xl transform hover:scale-105"
          >
            <ArrowLeft className="w-5 h-5 group-hover:-translate-x-1 transition-transform" />
            Back to Generator
            <Sparkles className="w-5 h-5 group-hover:rotate-12 transition-transform" />
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-slate-900 to-gray-900 relative overflow-hidden">
      {/* Animated Background Elements */}
      <div className="fixed inset-0 pointer-events-none">
        <div className="absolute top-20 left-10 w-32 h-32 bg-blue-800 rounded-full opacity-10 animate-pulse"></div>
        <div className="absolute top-40 right-20 w-24 h-24 bg-purple-800 rounded-full opacity-10 animate-pulse" style={{ animationDelay: '2s' }}></div>
        <div className="absolute bottom-40 left-20 w-20 h-20 bg-indigo-800 rounded-full opacity-10 animate-pulse" style={{ animationDelay: '4s' }}></div>
        <div className="absolute bottom-20 right-10 w-28 h-28 bg-cyan-800 rounded-full opacity-10 animate-pulse" style={{ animationDelay: '6s' }}></div>
      </div>

      {/* Enhanced Header */}
      <div className="relative z-10 bg-gray-900/80 backdrop-blur-xl border-b border-gray-700/50 shadow-xl">
        <div className="px-4 py-6 md:max-w-7xl md:mx-auto">
          <div className="flex items-center justify-between">
            <button
              onClick={handleGoBack}
              className="group flex items-center gap-3 px-4 py-2 bg-gray-800/50 hover:bg-gray-800 backdrop-blur-sm rounded-xl border border-gray-700/50 text-gray-300 hover:text-white transition-all duration-300 shadow-lg hover:shadow-xl"
            >
              <ArrowLeft className="w-5 h-5 group-hover:-translate-x-1 transition-transform" />
              <span className="font-medium">Back</span>
            </button>

            <div className="text-center">
              <div className="flex items-center gap-3 justify-center mb-2">
                <div className="relative">
                  <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center shadow-lg">
                    <Download className="w-6 h-6 text-white" />
                  </div>
                  <div className="absolute -top-1 -right-1">
                    <Sparkles className="w-4 h-4 text-yellow-400 animate-pulse" />
                  </div>
                </div>
                <h1 className="text-2xl md:text-3xl font-bold bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 bg-clip-text text-transparent">
                  Download Your Masterpiece
                </h1>
              </div>
              <p className="text-sm text-gray-400 font-medium">
                Your AI-generated artwork is ready for the world ✨
              </p>
            </div>

            <div className="w-20 md:w-24"></div> {/* Spacer for centering */}
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="px-2 py-4 md:max-w-6xl md:mx-auto md:px-4 md:py-8">
        <div className="flex flex-col md:flex-row gap-3 md:gap-6">
          {/* div1: Image + Image Details (flex-col) */}
          <div className="flex-1 flex flex-col gap-6 md:gap-8 relative z-10">
            {/* Enhanced Image Preview Box */}
            <div className="group relative">
              {/* Main Image Container */}
              <div className="bg-gray-800/70 backdrop-blur-xl rounded-3xl shadow-2xl overflow-hidden border border-gray-700/50 transform transition-all duration-500 hover:scale-[1.02] hover:shadow-3xl">
                {/* Image Stats Overlay */}
                <div className="absolute top-4 left-4 right-4 z-20 flex justify-between items-center">
                  <div className="flex items-center gap-2 px-3 py-1.5 bg-black/30 backdrop-blur-md rounded-full border border-white/20">
                    <Eye className="w-4 h-4 text-white" />
                    <span className="text-white text-sm font-medium">{imageSize}</span>
                  </div>
                  <div className="flex items-center gap-2 px-3 py-1.5 bg-black/30 backdrop-blur-md rounded-full border border-white/20">
                    <Camera className="w-4 h-4 text-white" />
                    <span className="text-white text-sm font-medium">AI Art</span>
                  </div>
                </div>

                {/* Quality Badge */}
                <div className="absolute top-4 right-4 z-20">
                  <div className="flex items-center gap-2 px-3 py-1.5 bg-gradient-to-r from-emerald-500 to-green-500 rounded-full shadow-lg">
                    <Award className="w-4 h-4 text-white" />
                    <span className="text-white text-sm font-medium">HD Quality</span>
                  </div>
                </div>

                <div className="relative overflow-hidden">
                  <img
                    src={imageUrl}
                    alt={prompt}
                    className="w-full h-auto object-contain max-h-96 md:max-h-none transition-transform duration-700 group-hover:scale-110"
                    onError={(e) => {
                      e.target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAwIiBoZWlnaHQ9IjQwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZjNmNGY2Ii8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCwgc2Fucy1zZXJpZiIgZm9udC1zaXplPSIxOCIgZmlsbD0iIzk5YTNhZiIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPkltYWdlIG5vdCBhdmFpbGFibGU8L3RleHQ+PC9zdmc+';
                    }}
                    onContextMenu={(e) => {
                      e.preventDefault();
                      handleShare();
                    }}
                  />

                  {/* Enhanced Hover Overlay */}
                  <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-all duration-500 flex items-center justify-center">
                    <div className="flex gap-4 transform translate-y-8 group-hover:translate-y-0 transition-transform duration-500">
                      <button
                        onClick={handleShare}
                        className="group/btn p-4 bg-white/90 backdrop-blur-sm rounded-2xl hover:bg-white transition-all duration-300 shadow-2xl transform hover:scale-110 hover:-translate-y-1"
                        title="Share Image"
                      >
                        <Share2 className="w-6 h-6 text-gray-700 group-hover/btn:text-blue-600 transition-colors" />
                      </button>
                      <button
                        onClick={async () => {
                          try {
                            const response = await fetch(imageUrl, { mode: 'cors' });
                            const blob = await response.blob();
                            const clipboardItem = new ClipboardItem({ [blob.type]: blob });
                            await navigator.clipboard.write([clipboardItem]);
                            alert('✅ Image copied to clipboard!');
                          } catch (err) {
                            await navigator.clipboard.writeText(imageUrl);
                            alert('📋 Image URL copied to clipboard!');
                          }
                        }}
                        className="group/btn p-4 bg-white/90 backdrop-blur-sm rounded-2xl hover:bg-white transition-all duration-300 shadow-2xl transform hover:scale-110 hover:-translate-y-1"
                        title="Copy Image"
                      >
                        <Copy className="w-6 h-6 text-gray-700 group-hover/btn:text-green-600 transition-colors" />
                      </button>
                      <button
                        onClick={() => window.open(imageUrl, '_blank')}
                        className="group/btn p-4 bg-white/90 backdrop-blur-sm rounded-2xl hover:bg-white transition-all duration-300 shadow-2xl transform hover:scale-110 hover:-translate-y-1"
                        title="View Full Size"
                      >
                        <ExternalLink className="w-6 h-6 text-gray-700 group-hover/btn:text-purple-600 transition-colors" />
                      </button>
                    </div>
                  </div>

                  {/* Mobile Interaction Hint */}
                  <div className="absolute bottom-4 left-4 right-4 md:hidden">
                    <div className="bg-black/60 backdrop-blur-md rounded-xl px-4 py-2 text-center border border-white/20">
                      <p className="text-white text-sm font-medium">
                        Tap and hold to share • Swipe for more options
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Floating Action Buttons (Mobile) */}
              <div className="absolute -bottom-6 left-1/2 transform -translate-x-1/2 flex gap-3 md:hidden">
                <button
                  onClick={handleShare}
                  className="p-3 bg-blue-500 hover:bg-blue-600 text-white rounded-full shadow-xl transform hover:scale-110 transition-all duration-300"
                >
                  <Share2 className="w-5 h-5" />
                </button>
                <button
                  onClick={() => window.open(imageUrl, '_blank')}
                  className="p-3 bg-purple-500 hover:bg-purple-600 text-white rounded-full shadow-xl transform hover:scale-110 transition-all duration-300"
                >
                  <ExternalLink className="w-5 h-5" />
                </button>
              </div>
            </div>

            {/* Enhanced Image Details Section */}
            <div className="bg-gray-800/70 backdrop-blur-xl rounded-3xl shadow-2xl p-6 md:p-8 border border-gray-700/50 transform transition-all duration-300 hover:shadow-3xl">
              <div className="flex items-center gap-3 mb-6">
                <div className="relative">
                  <div className="w-12 h-12 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-2xl flex items-center justify-center shadow-lg">
                    <Info className="w-6 h-6 text-white" />
                  </div>
                  <div className="absolute -top-1 -right-1">
                    <Sparkles className="w-4 h-4 text-yellow-400 animate-pulse" />
                  </div>
                </div>
                <h3 className="text-xl md:text-2xl font-bold bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent">
                  Image Details
                </h3>
              </div>

              <div className="space-y-6">
                {/* AI Prompt Section */}
                <div className="relative">
                  <div className="bg-gradient-to-r from-indigo-900/30 to-purple-900/30 rounded-2xl p-4 border border-indigo-700/50">
                    <div className="flex items-center gap-2 mb-3">
                      <Palette className="w-5 h-5 text-indigo-400" />
                      <span className="font-semibold text-indigo-200">AI Prompt:</span>
                    </div>
                    <p className="text-indigo-100 leading-relaxed font-medium">
                      "{prompt}"
                    </p>
                  </div>
                </div>

                {/* Technical Specifications Grid */}
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <div className="bg-gray-700/50 backdrop-blur-sm rounded-2xl p-4 border border-gray-600/50 text-center group hover:bg-gray-700 transition-all duration-300">
                    <div className="flex items-center justify-center mb-3">
                      <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-cyan-500 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform">
                        <Eye className="w-5 h-5 text-white" />
                      </div>
                    </div>
                    <span className="font-medium text-gray-300 block text-sm">Resolution</span>
                    <span className="text-white font-bold text-sm">
                      {/* hello */}
                      {imageSize}
                    </span>
                  </div>

                  <div className="bg-gray-700/50 backdrop-blur-sm rounded-2xl p-4 border border-gray-600/50 text-center group hover:bg-gray-700 transition-all duration-300">
                    <div className="flex items-center justify-center mb-3">
                      <div className="w-10 h-10 bg-gradient-to-br from-green-500 to-emerald-500 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform">
                        <ImageIcon className="w-5 h-5 text-white" />
                      </div>
                    </div>
                    <span className="font-medium text-gray-300 block text-sm">Format</span>
                    <span className="text-white font-bold text-lg">JPG</span>
                  </div>

                  <div className="bg-gray-700/50 backdrop-blur-sm rounded-2xl p-4 border border-gray-600/50 text-center group hover:bg-gray-700 transition-all duration-300">
                    <div className="flex items-center justify-center mb-3">
                      <div className="w-10 h-10 bg-gradient-to-br from-purple-500 to-pink-500 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform">
                        <Zap className="w-5 h-5 text-white" />
                      </div>
                    </div>
                    <span className="font-medium text-gray-300 block text-sm">Quality</span>
                    <span className="text-white font-bold text-lg">HD</span>
                  </div>

                  <div className="bg-gray-700/50 backdrop-blur-sm rounded-2xl p-4 border border-gray-600/50 text-center group hover:bg-gray-700 transition-all duration-300">
                    <div className="flex items-center justify-center mb-3">
                      <div className="w-10 h-10 bg-gradient-to-br from-orange-500 to-red-500 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform">
                        <Shield className="w-5 h-5 text-white" />
                      </div>
                    </div>
                    <span className="font-medium text-gray-300 block text-sm">License</span>
                    <span className="text-white font-bold text-lg">Free</span>
                  </div>
                </div>

                {/* Feature Tags */}
                <div className="flex flex-wrap gap-3">
                  <div className="flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-green-900/30 to-emerald-900/30 rounded-full border border-green-700/50">
                    <CheckCircle className="w-4 h-4 text-green-400" />
                    <span className="text-green-300 text-sm font-medium">Commercial Use</span>
                  </div>
                  <div className="flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-blue-900/30 to-cyan-900/30 rounded-full border border-blue-700/50">
                    <Clock className="w-4 h-4 text-blue-400" />
                    <span className="text-blue-300 text-sm font-medium">Generated Now</span>
                  </div>
                  <div className="flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-purple-900/30 to-pink-900/30 rounded-full border border-purple-700/50">
                    <Globe className="w-4 h-4 text-purple-400" />
                    <span className="text-purple-300 text-sm font-medium">AI Generated</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* div2: Advertisement + Download Section (flex-col) */}
          <div className="flex-1 flex flex-col gap-3 md:gap-6">
            {/* Advertisement Space */}
            <div>
              <AdvertisementSpace />
            </div>

            {/* Enhanced Download Section */}
            <div>
              <div className="bg-gray-800/70 backdrop-blur-xl rounded-3xl shadow-2xl p-6 md:p-8 border border-gray-700/50 transform transition-all duration-300 hover:shadow-3xl">
                <div className="text-center mb-8">
                  <div className="relative inline-block mb-6">
                    <div className="w-20 h-20 md:w-24 md:h-24 mx-auto bg-gradient-to-br from-emerald-500 via-green-500 to-teal-500 rounded-3xl flex items-center justify-center shadow-2xl transform transition-all duration-500 hover:scale-110 hover:rotate-3">
                      <Download className="w-10 h-10 md:w-12 md:h-12 text-white" />
                    </div>
                    <div className="absolute -top-2 -right-2 animate-bounce">
                      <div className="w-8 h-8 bg-gradient-to-r from-yellow-400 to-orange-400 rounded-full flex items-center justify-center shadow-lg">
                        <Sparkles className="w-4 h-4 text-white" />
                      </div>
                    </div>
                    <div className="absolute -bottom-2 -left-2 animate-pulse">
                      <div className="w-6 h-6 bg-gradient-to-r from-pink-400 to-rose-400 rounded-full flex items-center justify-center shadow-lg">
                        <Heart className="w-3 h-3 text-white" />
                      </div>
                    </div>
                  </div>

                  <h2 className="text-3xl md:text-4xl font-bold bg-gradient-to-r from-emerald-600 via-green-600 to-teal-600 bg-clip-text text-transparent mb-3">
                    Free Download
                  </h2>
                  <p className="text-lg text-gray-300 leading-relaxed font-medium">
                    Your AI masterpiece awaits! 🎨✨
                  </p>
                </div>

                {/* Enhanced Features Grid */}
                <div className="grid md:grid-cols-2 gap-4 mb-8">
                  <div className="bg-gradient-to-r from-emerald-900/30 to-green-900/30 rounded-2xl p-4 border border-emerald-700/50 group hover:shadow-lg transition-all duration-300">
                    <div className="flex items-center gap-3 mb-2">
                      <div className="w-8 h-8 bg-gradient-to-br from-emerald-500 to-green-500 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform">
                        <Eye className="w-4 h-4 text-white" />
                      </div>
                      <span className="font-semibold text-emerald-300">HD Quality</span>
                    </div>
                    <p className="text-sm text-emerald-400">High-resolution images</p>
                  </div>

                  <div className="bg-gradient-to-r from-blue-900/30 to-cyan-900/30 rounded-2xl p-4 border border-blue-700/50 group hover:shadow-lg transition-all duration-300">
                    <div className="flex items-center gap-3 mb-2">
                      <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-cyan-500 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform">
                        <ImageIcon className="w-4 h-4 text-white" />
                      </div>
                      <span className="font-semibold text-blue-300">JPG Format</span>
                    </div>
                    <p className="text-sm text-blue-400">Optimized compression & quality</p>
                  </div>

                  <div className="bg-gradient-to-r from-purple-900/30 to-pink-900/30 rounded-2xl p-4 border border-purple-700/50 group hover:shadow-lg transition-all duration-300">
                    <div className="flex items-center gap-3 mb-2">
                      <div className="w-8 h-8 bg-gradient-to-br from-purple-500 to-pink-500 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform">
                        <Shield className="w-4 h-4 text-white" />
                      </div>
                      <span className="font-semibold text-purple-300">Commercial Use</span>
                    </div>
                    <p className="text-sm text-purple-400">Free for personal & business</p>
                  </div>

                  <div className="bg-gradient-to-r from-orange-900/30 to-yellow-900/30 rounded-2xl p-4 border border-orange-700/50 group hover:shadow-lg transition-all duration-300">
                    <div className="flex items-center gap-3 mb-2">
                      <div className="w-8 h-8 bg-gradient-to-br from-orange-500 to-yellow-500 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform">
                        <Zap className="w-4 h-4 text-white" />
                      </div>
                      <span className="font-semibold text-orange-300">Instant Download</span>
                    </div>
                    <p className="text-sm text-orange-400">No waiting, get it now!</p>
                  </div>
                </div>

                {/* Enhanced Download Button */}
                <button
                  onClick={handleDownload}
                  disabled={isDownloading}
                  className={`group relative w-full py-5 md:py-6 px-8 md:px-10 rounded-2xl font-bold text-white text-lg md:text-xl transition-all duration-500 flex items-center justify-center gap-3 overflow-hidden ${
                    isDownloading
                      ? 'bg-gray-400 cursor-not-allowed'
                      : downloadComplete
                      ? 'bg-gradient-to-r from-emerald-500 to-green-500 animate-pulse shadow-2xl'
                      : 'bg-gradient-to-r from-emerald-500 via-green-500 to-teal-500 hover:from-emerald-600 hover:via-green-600 hover:to-teal-600 shadow-2xl hover:shadow-3xl transform hover:scale-105 hover:-translate-y-1'
                  }`}
                >
                  {/* Animated Background Effect */}
                  <div className="absolute inset-0 bg-gradient-to-r from-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500 transform -skew-x-12 translate-x-[-100%] group-hover:translate-x-[100%]"></div>

                  {isDownloading ? (
                    <>
                      <div className="w-6 h-6 border-3 border-white border-t-transparent rounded-full animate-spin"></div>
                      <span>Downloading Magic...</span>
                    </>
                  ) : downloadComplete ? (
                    <>
                      <CheckCircle className="w-7 h-7" />
                      <span>Downloaded Successfully! 🎉</span>
                    </>
                  ) : (
                    <>
                      <Download className="w-7 h-7 group-hover:animate-bounce" />
                      <span>Download Free HD Image</span>
                      <Sparkles className="w-5 h-5 group-hover:rotate-12 transition-transform" />
                    </>
                  )}
                </button>

                {/* Enhanced Alternative Download Methods */}
                <div className="mt-8 grid grid-cols-3 gap-3">
                  {/* Direct Download Link */}
                  <a
                    href={imageUrl}
                    download={`ai-generated-image-${Date.now()}.jpg`}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="group flex flex-col items-center gap-2 p-4 bg-gradient-to-br from-blue-900/30 to-cyan-900/30 hover:from-blue-900/50 hover:to-cyan-900/50 rounded-2xl border border-blue-700/50 transition-all duration-300 hover:shadow-lg hover:-translate-y-1"
                  >
                    <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-cyan-500 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform shadow-lg">
                      <Download className="w-5 h-5 text-white" />
                    </div>
                    <span className="text-xs font-semibold text-blue-300 text-center">Direct Link</span>
                  </a>

                  {/* Simple Download Button */}
                  <button
                    onClick={() => {
                      const link = document.createElement('a');
                      link.href = imageUrl;
                      link.download = `ai-generated-image-${Date.now()}.jpg`;
                      link.click();
                    }}
                    className="group flex flex-col items-center gap-2 p-4 bg-gradient-to-br from-emerald-900/30 to-green-900/30 hover:from-emerald-900/50 hover:to-green-900/50 rounded-2xl border border-emerald-700/50 transition-all duration-300 hover:shadow-lg hover:-translate-y-1"
                  >
                    <div className="w-10 h-10 bg-gradient-to-br from-emerald-500 to-green-500 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform shadow-lg">
                      <Zap className="w-5 h-5 text-white" />
                    </div>
                    <span className="text-xs font-semibold text-emerald-300 text-center">Quick Save</span>
                  </button>

                  {/* Open in New Tab */}
                  <button
                    onClick={() => window.open(imageUrl, '_blank')}
                    className="group flex flex-col items-center gap-2 p-4 bg-gradient-to-br from-purple-900/30 to-pink-900/30 hover:from-purple-900/50 hover:to-pink-900/50 rounded-2xl border border-purple-700/50 transition-all duration-300 hover:shadow-lg hover:-translate-y-1"
                  >
                    <div className="w-10 h-10 bg-gradient-to-br from-purple-500 to-pink-500 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform shadow-lg">
                      <ExternalLink className="w-5 h-5 text-white" />
                    </div>
                    <span className="text-xs font-semibold text-purple-300 text-center">New Tab</span>
                  </button>
                </div>



                {/* Enhanced Action Buttons */}
                <div className="space-y-6 mt-10">
                  {/* Primary Share Button */}
                  <button
                    onClick={handleShare}
                    className="group w-full py-4 px-6 bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white rounded-2xl transition-all duration-300 flex items-center justify-center gap-3 text-base font-semibold shadow-xl hover:shadow-2xl transform hover:scale-105 hover:-translate-y-1"
                  >
                    <Share2 className="w-5 h-5 group-hover:rotate-12 transition-transform" />
                    Share Your Creation
                    <Sparkles className="w-4 h-4 group-hover:animate-pulse" />
                  </button>

                  {/* Secondary Actions Grid */}
                  <div className="grid grid-cols-2 gap-4">
                    <button
                      onClick={async () => {
                        try {
                          const response = await fetch(imageUrl, { mode: 'cors' });
                          const blob = await response.blob();
                          const clipboardItem = new ClipboardItem({ [blob.type]: blob });
                          await navigator.clipboard.write([clipboardItem]);
                          alert('✅ Image copied to clipboard!');
                        } catch (err) {
                          await navigator.clipboard.writeText(imageUrl);
                          alert('📋 Image URL copied to clipboard!');
                        }
                      }}
                      className="group flex items-center justify-center gap-2 py-3 px-4 bg-gray-700/50 backdrop-blur-sm border-2 border-gray-600 hover:border-green-500 hover:bg-gray-700 rounded-xl text-gray-300 hover:text-green-400 transition-all duration-300 font-medium shadow-lg hover:shadow-xl transform hover:scale-105"
                    >
                      <Copy className="w-4 h-4 group-hover:scale-110 transition-transform" />
                      Copy Image
                    </button>

                    <button
                      className="group flex items-center justify-center gap-2 py-3 px-4 bg-gray-700/50 backdrop-blur-sm border-2 border-gray-600 hover:border-pink-500 hover:bg-gray-700 rounded-xl text-gray-300 hover:text-pink-400 transition-all duration-300 font-medium shadow-lg hover:shadow-xl transform hover:scale-105"
                    >
                      <Bookmark className="w-4 h-4 group-hover:scale-110 transition-transform" />
                      Save
                    </button>
                  </div>

                  {/* Social Stats */}
                  <div className="flex justify-center gap-8 pt-6 border-t border-gray-600/50">
                    <div className="text-center group cursor-pointer">
                      <div className="flex items-center gap-1 text-gray-400 group-hover:text-blue-400 transition-colors">
                        <Users className="w-4 h-4" />
                        <span className="text-sm font-semibold">2.1k</span>
                      </div>
                      <span className="text-xs text-gray-500">Downloads</span>
                    </div>
                    <div className="text-center group cursor-pointer">
                      <div className="flex items-center gap-1 text-gray-400 group-hover:text-pink-400 transition-colors">
                        <Heart className="w-4 h-4" />
                        <span className="text-sm font-semibold">156</span>
                      </div>
                      <span className="text-xs text-gray-500">Likes</span>
                    </div>
                    <div className="text-center group cursor-pointer">
                      <div className="flex items-center gap-1 text-gray-400 group-hover:text-purple-400 transition-colors">
                        <Share2 className="w-4 h-4" />
                        <span className="text-sm font-semibold">89</span>
                      </div>
                      <span className="text-xs text-gray-500">Shares</span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Enhanced Terms Section */}
              <div className="text-center mt-8 p-6 bg-gray-800/50 backdrop-blur-sm rounded-3xl border border-gray-700/50 shadow-lg">
                <div className="flex items-center justify-center gap-2 mb-3">
                  <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center">
                    <Shield className="w-4 h-4 text-white" />
                  </div>
                  <span className="text-sm font-semibold text-gray-300">Legal & Usage Rights</span>
                </div>
                <p className="text-sm text-gray-400 leading-relaxed">
                  By downloading, you agree to our{' '}
                  <button
                    onClick={handleTermsClick}
                    className="text-blue-400 hover:text-blue-300 underline font-medium transition-colors"
                  >
                    Terms of Service
                  </button>
                  {' '}• Free for personal & commercial use ✨
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ImageDownload;
