import axios from 'axios';

const API_BASE_URL = `${import.meta.env.VITE_API_BASE_URL}/api`;

// Create axios instance with default config
const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
  timeout: 120000, // 2 minutes timeout for image generation
});

// Generate image from text prompt
export const generateImage = async (prompt, aspectRatio = '1:1') => {
  try {
    const response = await api.post('/ai/getResult', {
      prompt: prompt.trim(),
      aspectRatio: aspectRatio,
    });

    const result = {
      success: true,
      imageUrl: response.data.imageUrl,
      prompt: prompt,
      status: response.data.status,
      message: response.data.message
    };

    // Start ImageKit URL retrieval in background (don't wait for it)
    handleImageKitUrlAsync(response.data.imageUrl, prompt.trim(), aspectRatio);

    return result;
  } catch (error) {
    let errorMessage = 'Failed to generate image. Please try again.';

    if (error.response) {
      // Server responded with error status
      errorMessage = error.response.data?.message || `Server error: ${error.response.status}`;
    } else if (error.request) {
      // Request was made but no response received
      errorMessage = 'No response from server. Please check if the backend is running.';
    } else {
      // Something else happened
      errorMessage = error.message || 'An unexpected error occurred.';
    }

    return {
      success: false,
      error: errorMessage,
    };
  }
};

// Handle ImageKit URL retrieval and localStorage saving asynchronously
const handleImageKitUrlAsync = async (originalImageUrl, prompt, aspectRatio) => {
  console.log('🔄 Starting ImageKit URL retrieval for:', originalImageUrl);

  // Wait a bit for the backend to upload to ImageKit
  await new Promise(resolve => setTimeout(resolve, 3000));

  try {
    const imagekitResponse = await getImageKitUrl(originalImageUrl);
    console.log('📥 ImageKit response:', imagekitResponse);

    if (imagekitResponse.success && imagekitResponse.imageKitUrl) {
      // Save ImageKit URL to localStorage
      const imageData = {
        imageUrl: imagekitResponse.imageKitUrl, // Use ImageKit URL as the main image URL
        prompt: prompt,
        aspectRatio: aspectRatio,
        timestamp: new Date().toISOString(),
        isImageKit: true,
        originalUrl: originalImageUrl // Keep reference to original URL
      };

      try {
        // Get existing images from localStorage
        const existingImages = JSON.parse(localStorage.getItem('generatedImages') || '[]');
        console.log('📂 Existing images in localStorage:', existingImages.length);

        // Add new image to the beginning
        existingImages.unshift(imageData);
        console.log('➕ Added new image data:', imageData);

        // Keep only last 50 images to prevent localStorage overflow
        const limitedImages = existingImages.slice(0, 50);

        // Save back to localStorage
        localStorage.setItem('generatedImages', JSON.stringify(limitedImages));
        console.log('💾 Saved to localStorage. Total images:', limitedImages.length);

        console.log('✅ ImageKit URL saved to localStorage for History page');

        // Dispatch a custom event to notify components that history was updated
        window.dispatchEvent(new CustomEvent('historyUpdated', {
          detail: { imageData, totalImages: limitedImages.length }
        }));

      } catch (storageError) {
        console.error('❌ Failed to save ImageKit URL to localStorage:', storageError);
      }
    } else {
      console.log('⚠️ ImageKit URL not available yet or failed to upload');
    }
  } catch (error) {
    console.error('❌ Error getting ImageKit URL:', error);
  }
};

// Get ImageKit URL for an image
export const getImageKitUrl = async (imageUrl) => {
  try {
    const response = await api.get('/ai/getImageKitUrl', {
      params: { imageUrl }
    });

    return {
      success: true,
      imageKitUrl: response.data.imageKitUrl,
      imageKitFileId: response.data.imageKitFileId,
      imageKitFilePath: response.data.imageKitFilePath,
      hasImageKit: response.data.hasImageKit
    };
  } catch (error) {
    console.error('ImageKit API Error:', error);
    return {
      success: false,
      error: error.response?.data?.error || 'Failed to get ImageKit URL'
    };
  }
};



// Get images from localStorage
export const getImagesFromLocalStorage = () => {
  try {
    const images = JSON.parse(localStorage.getItem('generatedImages') || '[]');
    // Convert to history format with proper IDs
    return images.map((img, index) => ({
      id: img.timestamp || Date.now() - index, // Use timestamp or generate ID
      imageUrl: img.imageUrl, // This will be ImageKit URL
      prompt: img.prompt,
      aspectRatio: img.aspectRatio,
      createdAt: img.timestamp,
      timestamp: img.timestamp || Date.now() - index,
      isImageKit: img.isImageKit || false
    }));
  } catch (error) {
    console.error('❌ Failed to get images from localStorage:', error);
    return [];
  }
};

// Health check for backend
export const checkBackendHealth = async () => {
  try {
    const response = await api.get('/health');
    return response.status === 200;
  } catch (error) {
    return false;
  }
};

export default api;
