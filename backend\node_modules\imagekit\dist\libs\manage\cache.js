"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
/*
    Constants
*/
var errorMessages_1 = __importDefault(require("../constants/errorMessages"));
/*
    Utils
*/
var respond_1 = __importDefault(require("../../utils/respond"));
var request_1 = __importDefault(require("../../utils/request"));
var purgeCache = function (url, defaultOptions, callback) {
    if (!url && !url.length) {
        (0, respond_1.default)(true, errorMessages_1.default.CACHE_PURGE_URL_MISSING, callback);
        return;
    }
    var requestOptions = {
        url: "https://api.imagekit.io/v1/files/purge",
        method: "POST",
        json: {
            url: url,
        },
    };
    (0, request_1.default)(requestOptions, defaultOptions, callback);
};
var getPurgeCacheStatus = function (requestId, defaultOptions, callback) {
    if (!requestId && !requestId.length) {
        (0, respond_1.default)(true, errorMessages_1.default.CACHE_PURGE_STATUS_ID_MISSING, callback);
        return;
    }
    var requestOptions = {
        url: "https://api.imagekit.io/v1/files/purge/" + requestId,
        method: "GET",
    };
    (0, request_1.default)(requestOptions, defaultOptions, callback);
};
exports.default = { purgeCache: purgeCache, getPurgeCacheStatus: getPurgeCacheStatus };
